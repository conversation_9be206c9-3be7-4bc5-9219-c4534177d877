# AutoApiTest

## 项目简介
这是一个基于Apifox的后端接口自动化测试项目，用于自动化测试API接口和场景，支持多种环境和VPN配置。项目集成了Jenkins CI/CD，支持飞书消息通知和测试报告解析功能。

## 项目结构
```
├── .config/                 # 配置目录
├── .git/                    # Git版本控制目录
├── .idea/                   # IDE配置文件
├── .venv/                   # Python虚拟环境
├── apifox_project_data/     # Apifox项目数据
├── apifox-reports/          # 测试报告输出目录
├── examples/                # 测试用例示例
│   ├── api/                 # API通用测试用例
│   ├── eap/                 # EAP系统测试用例
│   ├── oppo/                # OPPO系统测试用例
│   └── zte/                 # 中兴努比亚系统测试用例
├── msg/                     # 消息通知模块
│   ├── conf.py              # 飞书消息配置
│   └── feishupush.py        # 飞书推送功能
├── script/                  # 测试脚本
│   ├── api/                 # API测试脚本
│   ├── bgm/                 # BGM系统脚本
│   ├── fuli/                # 福利系统脚本
│   ├── qingting/            # 蜻蜓系统脚本
│   ├── sjy/                 # 神经元系统脚本
│   ├── ultimate/            # Ultimate系统脚本
│   └── ximalaya/            # 喜马拉雅系统脚本
├── tool/                    # 工具脚本
│   ├── parse_report.py      # Apifox报告解析工具
│   └── README_parse_report.md # 报告解析工具说明
├── .gitignore               # Git忽略文件配置
├── Jenkinsfile              # Jenkins CI/CD配置文件
├── pyproject.toml           # Python-UV项目配置
├── requirements.txt         # Python依赖包
├── README.md                # 项目说明文档
└── uv.lock                  # UV依赖锁文件
```

## 环境依赖
- Python >= 3.10
- Redis >= 3.5.3
- Redis-py-cluster >= 2.1.3
- Pycryptodome >= 3.22.0
- Beautifulsoup4 >= 4.13.4
- Lxml >= 6.0.0
- Pandas >= 2.3.0
- Requests >= 2.32.4
- NodeJS >= 16.16.0（用于安装Apifox CLI）

## 安装与配置
1. 克隆本项目
```bash
git clone http://git.allsaints.top/auto_test/AutoApiTest.git
cd AutoApiTest
```

2. 配置Python虚拟环境
```bash
# 安装uv工具（根据对应系统安装）

# Mac/Linux/Windows系统通用方法：（pip安装uv，前提本地系统已经安装了python）
pip install uv -i https://mirrors.aliyun.com/pypi/simple/

# Windows系统方法：（powershell终端命令安装）
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex" 
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Mac/Linux系统方法：（curl命令安装）
curl -sSf https://github.com/astral-sh/uv/releases/latest/download/uv-installer.sh | bash

# 使用uv创建虚拟环境（前提打开终端）
# Windows/Mac/Linux系统
cd AutoApiTest # 进入到项目目录
uv venv --python 3.10.14

# 激活虚拟环境
# Windows系统
.venv\Scripts\activate

# Mac/Linux系统
source .venv/bin/activate
```

3. 安装依赖
```bash
# 使用uv自动同步项目依赖(如果项目内没有使用uv来管理python依赖，可以使用 uv init 初始化项目（推荐）)
uv sync

# 添加新依赖(示例) [-i] 为添加国内镜像源
uv add <package_name> -i https://mirrors.aliyun.com/pypi/simple/

# 移除依赖(示例)
uv remove <package_name>

# 升级依赖(示例)
uv add --upgrade <package_name>

# 导1出所有已安装包到 requirements.txt
uv pip freeze > requirements.txt
```

4. 安装Apifox CLI（jenkins服务器使用，本地开发环境无需安装）
```bash
npm i -g apifox-cli@latest --registry=https://registry.npmmirror.com/
```

## 使用方法
### 通过Jenkins执行自动化测试
项目已配置Jenkins流水线，可通过Jenkins执行自动化测试：
1. 访问Jenkins项目页面
2. 选择对应分支
3. 选择对应项目跟产品
5. 点击构建按钮

### jenkins服务器执行测试（本地开发环境请直接使用apifox客户端!）
可以使用Apifox CLI在本地执行测试：
```bash
apifox run examples/xx.apifox-cli.json -r cli,html
```

### git切换远程地址方法
```bash
# 查看当前远程地址
git remote -v
# 切换到新的远程地址
git remote set-url origin 项目远程地址(如：*********************:auto_test/AutoApiTest.git)
# 验证是否切换成功
git remote -v
```

## VPN配置
如需在特定网络环境下测试，可配置VPN：
1. 将VPN配置文件放入`openvpn/`目录
2. 执行测试时选择相应的VPN配置

## 测试报告
测试完成后，报告将生成在：
- Jenkins环境：http://test-hw-jenkins.allsaints.group:8080/job/autoTest/job/<job_name>/<build_number>/artifact/
- 本地环境：`./apifox-reports/`目录

### 报告解析功能
项目包含自动解析Apifox报告的功能：
```python
from tool.parse_report import parse_apifox_report

# 解析报告文件
report_data = parse_apifox_report("apifox-reports/<project>_<backend>_report_<build_number>.html")
```

## 飞书通知
测试完成后，结果将通过飞书机器人自动推送到指定的飞书群：
- 测试通过：显示绿色通过标识
- 测试失败：显示红色失败标识

通知内容包含：
- 测试场景名称
- 测试结果状态
- Git提交信息
- 测试用例统计（通过/失败）
- 接口请求统计
- 断言统计
- 测试报告链接

## 常用工具脚本
- `script/api/getSign.py`: API签名生成工具
- `script/api/main.py`: Redis数据操作示例
- `script/qingting/getKey.py`: 蜻蜓系统签名生成
- `tool/parse_report.py`: 解析Apifox测试报告工具

## 贡献指南
1. Fork本项目
2. 创建您的特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交您的更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 联系方式
如有任何问题，请联系项目维护者：<EMAIL>
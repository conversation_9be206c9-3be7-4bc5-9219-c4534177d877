#!/bin/bash

# 如果任何命令执行失败，立即退出脚本
set -e

# --- 变量设置 ---
# 使用Zadig内置变量
PROJECT_NAME=${PROJECT_NAME} 
BACKEND_NAME=${BACKEND_NAME} 
GIT_PROJECT=${REPO_0}
PROJECT=${PROJECT}
eval GIT_BRANCH=\${${GIT_PROJECT}_BRANCH}
eval GIT_TAG=\${${GIT_PROJECT}_TAG} 
eval GIT_COMMIT=\${${GIT_PROJECT}_COMMIT_ID} 

echo "zadig项目: ${PROJECT}"
echo "测试项目: ${PROJECT_NAME}"
echo "测试模块: ${BACKEND_NAME}"
echo "测试任务URL: ${TASK_URL}"
echo "代码库名称: ${GIT_PROJECT}"
echo "GIT分支: ${GIT_BRANCH}"
echo "GIT_TAG: ${GIT_TAG}"
echo "GIT_COMMITID: ${GIT_COMMIT}"

# 进入代码库--zadig不会自动进入代码库，这里需要手动进入
cd ${GIT_PROJECT} 
echo "项目路径: $(pwd)"

# --- 安装/更新 Apifox CLI ---
echo "====== 检查并安装/更新 Apifox CLI ======"
# 获取本地安装的 apifox-cli 版本, 如果未安装则返回 "not installed"
LOCAL_VERSION=$(apifox -v 2>/dev/null || echo "not installed")
# 从 npm 仓库获取最新版本
LATEST_VERSION=$(npm view apifox-cli version --registry=https://registry.npmmirror.com/ 2>/dev/null || echo "unknown")
# 检查是否需要安装或更新
if [ "$LOCAL_VERSION" = "not installed" ]; then
    echo "Apifox CLI 未安装，正在安装最新版本 ${LATEST_VERSION}..."
    npm i -g apifox-cli@latest --registry=https://registry.npmmirror.com/
elif [ "$LOCAL_VERSION" != "$LATEST_VERSION" ]; then
    echo "Apifox CLI 需要更新，本地版本: ${LOCAL_VERSION}，最新版本: ${LATEST_VERSION}"
    npm i -g apifox-cli@latest --registry=https://registry.npmmirror.com/
else
    echo "Apifox CLI 已是最新版本 ${LOCAL_VERSION}，无需重新安装。"
fi

echo "====== Apifox CLI 安装/更新完成 ======"
echo ""


# --- 同步 Python 依赖包 ---
echo "====== 同步 Python 依赖包 ======"
if [ -f "requirements.txt" ]; then
    pip3 install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/
    echo "====== Python 依赖包同步完成 ======"
else
    echo "requirements.txt 文件不存在，跳过依赖安装"
fi
echo ""

# --- 执行 ApiFox 测试场景 ---
echo "====== 执行 ApiFox 测试场景 ======"

# 检查是否是OPPO华为云灾备环境测试
HOSTS_MODIFIED=false

if [ -n "$BACKEND_NAME" ] && [ "$BACKEND_NAME" = "OPPO灾备环境主流程验证" ]; then
    echo "检测到OPPO灾备环境主流程验证，需要临时修改hosts文件"
    
    # 备份原hosts文件
    if command -v sudo >/dev/null 2>&1; then
        sudo cp /etc/hosts /etc/hosts.bak
        
        # 添加临时hosts记录-指向华为云灾备测试环境的LB的IP
        sudo tee -a /etc/hosts << EOF
************     api-server.allsaints.top
************     oppo-mbi.allsaintsmusic.com 
************     msg2-oppo-mbi.allsaints.top 
************     m-api-server.allsaints.top 
************     msg2-mi-mbi.allsaints.top
EOF
    else
        # 如果没有sudo权限，记录错误但继续执行
        echo "警告: 没有sudo权限，无法修改hosts文件"
    fi
    echo "已添加临时hosts记录"
    HOSTS_MODIFIED=true
fi

# 执行测试

if [ -n "$PROJECT_NAME" ] && [ -n "$BACKEND_NAME" ]; then
    if [ -f "./examples/${PROJECT_NAME}/${BACKEND_NAME}.apifox-cli.json" ]; then
        apifox run "./examples/${PROJECT_NAME}/${BACKEND_NAME}.apifox-cli.json" -r cli,html --verbose --out-file "${PROJECT_NAME}_${BACKEND_NAME}_report"

    else
        echo "错误: 测试配置文件 examples/${PROJECT_NAME}/${BACKEND_NAME}.apifox-cli.json 不存在"
    fi
else
    echo "错误: PROJECT_NAME 或 BACKEND_NAME 未设置"
fi

# 如果修改了hosts文件，测试完成后恢复原hosts文件
if [ "$HOSTS_MODIFIED" = "true" ] && command -v sudo >/dev/null 2>&1; then
    echo "恢复原hosts文件"
    sudo cp /etc/hosts.bak /etc/hosts
    sudo rm /etc/hosts.bak
fi

echo "====== ApiFox 测试场景执行完成 ======"
echo ""

# --- 推送结果到飞书群 ---
echo "====== 推送结果到飞书群 ======"

# 使用Python生成配置JSON（避免手动转义和格式问题）
CONF=$(python3 -c "
import json
print(json.dumps({
    'JOB_NAME': '$PROJECT',
    'GIT_BRANCH': '$GIT_BRANCH',
    'GIT_TAG': '$GIT_TAG',
    'GIT_COMMIT': '$GIT_COMMIT',
    'LOG_URL': '$TASK_URL',
    'APIFOX_CASE_NAME': '${PROJECT_NAME}_${BACKEND_NAME}',
    'PROJECT_NAME': '$PROJECT_NAME',
    'BACKEND_NAME': '$BACKEND_NAME',
}))
")

echo "配置JSON: $CONF"
# 推送消息
if [ -f "./msg/feishupush_zadig.py" ]; then
    python3 ./msg/feishupush_zadig.py "$CONF"
    echo "====== 飞书推送完成 ======"
else
    echo "警告: 飞书推送脚本不存在，跳过推送"
fi
echo ""

echo "====== 测试执行完毕 ======"

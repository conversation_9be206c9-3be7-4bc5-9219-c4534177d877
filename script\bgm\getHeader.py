# -*- coding:utf-8 -*-
# @Time: 2023/12/21 15:30
# @Author: wenjie.hu
# @Email: <EMAIL>
# BGM接口生成key脚本--nacos配置查询DATAID:bgm-test

import hashlib
import hmac
import uuid
import time
import sys


class EncryptUtils:
    """
        headers参数加密方法
    """
    HEXDIGITS = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f"]
    HMAC_SHA1_ALGORITHM = "HmacSHA1"
    MD5_ALGORITHM = "MD5"
    CHARSET = "UTF-8"

    @staticmethod
    def hmac(secret, data):
        try:
            sks = hmac.new(secret.encode(EncryptUtils.CHARSET), data.encode(EncryptUtils.CHARSET), hashlib.sha1)
            return EncryptUtils.byteArrayToHexString(sks.digest())
        except Exception as e:
            raise RuntimeError(e)

    @staticmethod
    def md5(data):
        try:
            md = hashlib.md5()
            md.update(data.encode(EncryptUtils.CHARSET))
            return EncryptUtils.byteArrayToHexString(md.digest())
        except Exception as e:
            raise RuntimeError(e)

    @staticmethod
    def byteArrayToHexString(data):
        buffer = ""
        for byte in data:
            buffer += EncryptUtils.byteToHexString(byte)
        return buffer

    @staticmethod
    def byteToHexString(b):
        n = b if b >= 0 else 256 + b
        d1 = n // 16
        d2 = n % 16
        return EncryptUtils.HEXDIGITS[d1] + EncryptUtils.HEXDIGITS[d2]

    @staticmethod
    def get_headers(b_env):
        # 默认测试环境key(nacos配置查询Data ID:bgm-test)
        appid = "oppobgm"
        sign = "fcdxxxxxxxxxxxxxxxxxxxxxxxxx0e6a"  # fcdxxxxxxxxxxxxxxxxxxxxxxxxx0e6a
        if b_env == "prod":  # 正式环境则切换key
            appid = "opposclk"
            sign = "ejcnaaswhaa4micPYjJWN4bOibVef3bV"  # ejcnaaswhaa4micPYjJWN4bOibVef3bV
        head = {
            "appid": appid,  # opposclk
            "nonce": str(uuid.uuid4()),
            "ts": str(int(time.time() * 1000))
        }

        head['sign'] = EncryptUtils.hmac(sign,
                                         "appid={}&nonce={}&ts={}".format(head['appid'], head['nonce'], head['ts']))
        print(head)  # 这里直接输出返回的数组内容，可以给apifox自定义脚本使用


if __name__ == '__main__':
    cmd_text = sys.argv[1]  # 获取脚本传入的内容
    bgm_env = sys.argv[2]
    # print(bgm_env)
    if cmd_text == "get_headers":  # 如果是get_headers方法，则调用
        EncryptUtils.get_headers(bgm_env)

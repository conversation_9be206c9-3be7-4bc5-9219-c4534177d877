{"apifoxCli": "1.2.21", "item": [{"item": [{"id": "15d28630-fe0e-5690-b59e-a1026d7be351", "type": "group", "metaInfo": {"id": "15d28630-fe0e-5690-b59e-a1026d7be351", "type": "group", "scopeType": "start", "scopeEndId": "1943c250-ec68-59d7-bb8b-21b36b0ae914", "name": "root"}}, {"id": "962b5ae7-267b-55a8-817c-f0e36c5b8adb", "name": "获取token", "request": {"url": {"protocol": "https", "path": ["<PERSON><PERSON><PERSON><PERSON>", "auth", "oauth2", "token"], "host": ["test-upmp", "allsaints", "group"], "query": [{"disabled": false, "key": "t", "value": "{{$date.millisecondsTimestamp}}"}, {"disabled": false, "key": "scope", "value": "server"}, {"disabled": false, "key": "grant_type", "value": "password"}, {"disabled": false, "key": "username", "value": "{{username}}"}], "variable": []}, "header": [{"disabled": false, "key": "authorization", "value": "Basic dXBtcDozUllyclNROGtjQWdGOGVOa3owelZ3bz0="}, {"key": "<PERSON><PERSON>", "value": "d-locale=zh-CN"}, {"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}], "method": "POST", "baseUrl": "https://test-upmp.allsaints.group", "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"disabled": false, "key": "password", "value": "{{password}}"}]}, "type": "http"}, "response": [], "event": [{"listen": "test", "script": {"id": "postProcessors.0.extractor", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "      async function ____replaceIn(value) {", "        if (typeof pm.variables.replaceInAsync === 'function') {", "          return await pm.variables.replaceInAsync(value);", "        }", "        return pm.variables.replaceIn(value);", "      };", "      ;(async () => {", "        try{", "          ", "          const expression = pm.variables.replaceIn(`$.access_token`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          const jsonData = pm.response.json();", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "          ", "          ", "        switch (typeof value) {", "          case 'object':", "            value = JSON.stringify(value);", "            break;", "          default:", "            value = String(value);", "            break;", "        }", "      ", "          const formattedName = await ____replaceIn(`access_token`);pm.environment.set(formattedName, value);console.log('___label_placeholder__processor___', '已设置环境变量【' + formattedName + '】，值为' + ' ' + '【' + value + '】')", "        } catch(e) {", "          e.message = `提取变量【access_token】出错: ` + e.message;", "          setImmediate(() => { throw e });", "        }", "      })();", "      "]}}], "responseDefinition": {"id": 486870571, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"sub": {"type": "string"}, "clientId": {"type": "string"}, "code": {"type": "string"}, "iss": {"type": "string"}, "token_type": {"type": "string"}, "access_token": {"type": "string"}, "refresh_token": {"type": "string"}, "aud": {"type": "array", "items": {"type": "string"}}, "license": {"type": "string"}, "nbf": {"type": "string"}, "user_info": {"type": "object", "properties": {"username": {"type": "string"}, "authorities": {"type": "array", "items": {"type": "object", "properties": {"role": {"type": "string"}}, "required": ["role"]}}, "accountNonExpired": {"type": "boolean"}, "accountNonLocked": {"type": "boolean"}, "credentialsNonExpired": {"type": "boolean"}, "enabled": {"type": "boolean"}, "attributes": {"type": "object", "properties": {}}, "id": {"type": "string"}, "deptId": {"type": "string"}, "email": {"type": "string"}, "mfaEnabled": {"type": "boolean"}, "mfaSecret": {"type": "string"}, "nickname": {"type": "string"}}, "required": ["username", "authorities", "accountNonExpired", "accountNonLocked", "credentialsNonExpired", "enabled", "attributes", "id", "deptId", "email", "mfaEnabled", "mfaSecret", "nickname"]}, "user_id": {"type": "integer"}, "needBinding": {"type": "boolean"}, "scope": {"type": "array", "items": {"type": "string"}}, "exp": {"type": "string"}, "expires_in": {"type": "integer"}, "iat": {"type": "string"}, "jti": {"type": "string"}, "username": {"type": "string"}}, "required": ["sub", "clientId", "code", "iss", "token_type", "access_token", "refresh_token", "aud", "license", "nbf", "user_info", "user_id", "needBinding", "scope", "exp", "expires_in", "iat", "jti", "username"]}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "oasExtensions": "", "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "bcf8377d-4cb9-468f-bad6-787afd0ea1cd", "id": "962b5ae7-267b-55a8-817c-f0e36c5b8adb", "type": "http", "name": "获取token", "projectId": 4800107, "relatedId": 6669832, "environmentId": 24915297, "blockNumber": 1, "httpApiId": 192391555, "httpApiCaseId": 268234263, "httpApiName": "获取token", "httpApiPath": "/upmpsapi/auth/oauth2/token", "httpApiMethod": "post", "httpApiCaseName": "获取token"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "bfa36f84-d61c-598e-bd5b-ebedbf9404b5", "name": "删除歌曲评论脏数据", "request": {"url": {"protocol": "https", "path": ["uss", "api", "admin", "feedback", "comment", "song", "page"], "host": ["test-upmp", "allsaints", "group"], "query": [{"disabled": false, "key": "page", "value": "1"}, {"disabled": false, "key": "size", "value": "50"}, {"disabled": false, "key": "commentType", "value": "1"}], "variable": []}, "header": [{"disabled": false, "key": "Authorization", "value": "Bearer {{access_token}}"}, {"disabled": false, "key": "Pragma", "value": "no-cache"}, {"disabled": false, "key": "channel", "value": "{{channel}}"}, {"disabled": false, "key": "operator", "value": "liumei"}, {"disabled": false, "key": "region", "value": "CN"}, {"key": "<PERSON><PERSON>", "value": "access_token=-EHPeW-UUYFLaLszRoCs1gGYOd_G0cxcXLwEDjuRzV5IeMfUXYDREJAOq0vYu2KcZoryHFll_hUCrq9Sg0T9kvIBE1gAW7aBjIVDrHUwmrmPKZygAckpVWdhY0TA4b37; refresh_token=RobdKgOWodqxZgf5SC14I-g7U7tBUt9_fTa6ULzjEpUA6B0VWx24Dg1b3fWsAp8_HE-YexhekYHUVrqZqmqlBXe0aMKJ62rQCzyKD4p5EzIQXids1HPjRmMLHKFTGO0i; d-locale=zh-CN"}, {"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}], "method": "GET", "baseUrl": "https://test-upmp.allsaints.group", "body": {}, "auth": {"type": "<PERSON><PERSON><PERSON>", "noauth": []}, "type": "http"}, "response": [], "event": [{"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        var jsonData = pm.response.json();", "", "//提取token", "var token = pm.variables.get(\"access_token\")", "var channel = pm.environment.get(\"channel\");", "", "//筛选响应体脏数据id", "var deleteDataId = jsonData.data.list.filter(item => item.content.includes('自动化')).filter(item => item.state != 4).map(item => item.id);", "", "//若未发现脏数据则退出", "if (deleteDataId.length == 0) {", "    console.log(\"未发现content含'自动化'字符串的脏数据\");", "    return;", "}", "", "for (i = 0; i < deleteDataId.length; i++) {", "    console.log(\"第\" + (i + 1) + \"个脏数据id是\" + deleteDataId[i]);", "}", "", "// 设置删除接口URL", "var deleteRequestUrl = \"https://test-upmp.allsaints.group/uss/api/admin/feedback/comment/delete\";", "", "pm.sendRequest({", "    url: deleteRequestUrl,", "    method: \"POST\",", "    header: {", "        \"Authorization\": \"Bearer \" + token,", "        \"Content-Type\": \"application/json\",", "        \"channel\": channel,", "        \"operator\": \"cmsAutoTest\",", "        \"region\": \"CN\"", "    },", "    body: {", "        mode: \"raw\",", "        raw: JSON.stringify({ ids: deleteDataId, commentType: 1, reason: \"删除自动化数据\" })", "    }", "}, (err, res) => {", "    if (err) {", "        console.error(\"删除失败:\", err);", "        return;", "    } else {", "        if (res.json().message == \"ok\") {", "            console.log(\"歌曲评论脏数据删除完成\");", "        }", "        else {", "            pm.test(\"删除失败原因确认\", () => {", "                pm.expect.fail(`错误码: ${res.json().code}, 信息: ${res.json().message}`);", "            });", "        }", "    }", "});", "      "]}}], "responseDefinition": {"id": 579298205, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"totalCount": {"type": "integer"}, "list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "uid": {"type": "string"}, "content": {"type": "string"}, "reviewSource": {"type": "integer"}, "commentType": {"type": "integer"}, "resId": {"type": "string"}, "resName": {"type": "string"}, "createTime": {"type": "integer"}, "updateTime": {"type": "integer"}, "likeNum": {"type": "integer"}, "state": {"type": "integer"}, "approvalStatus": {"type": "integer"}, "yiDunStatus": {"type": "integer"}, "fuid": {"type": "string"}, "nickName": {"type": "string"}, "disable": {"type": "boolean"}, "commentSource": {"type": "integer"}}, "x-apifox-orders": ["id", "uid", "content", "reviewSource", "commentType", "resId", "resName", "createTime", "updateTime", "likeNum", "state", "approvalStatus", "yiDunStatus", "fuid", "nick<PERSON><PERSON>", "disable", "commentSource"]}}}, "required": ["totalCount", "list"], "x-apifox-orders": ["totalCount", "list"]}, "success": {"type": "boolean"}}, "required": ["code", "message", "data"], "x-apifox-orders": ["code", "message", "data", "success"]}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "oasExtensions": "", "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "a1cbb847-140b-4aeb-a743-a5d56c3e39d8", "id": "bfa36f84-d61c-598e-bd5b-ebedbf9404b5", "type": "http", "name": "删除歌曲评论脏数据", "projectId": 4800107, "relatedId": 6669832, "environmentId": 24915297, "blockNumber": 2, "httpApiId": 241237383, "httpApiCaseId": 268234264, "httpApiName": "uss查询歌曲评论", "httpApiPath": "/uss/api/admin/feedback/comment/song/page", "httpApiMethod": "get", "httpApiCaseName": "uss查询歌曲评论"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "96555d77-b43e-595c-b346-d930a385d133", "name": "删除专辑评论脏数据", "request": {"url": {"protocol": "https", "path": ["uss", "api", "admin", "feedback", "comment", "album", "page"], "host": ["test-upmp", "allsaints", "group"], "query": [{"disabled": false, "key": "page", "value": "1"}, {"disabled": false, "key": "size", "value": "50"}, {"disabled": false, "key": "commentType", "value": "3"}], "variable": []}, "header": [{"disabled": false, "key": "Authorization", "value": "Bearer {{access_token}}"}, {"disabled": false, "key": "Pragma", "value": "no-cache"}, {"disabled": false, "key": "channel", "value": "{{channel}}"}, {"disabled": false, "key": "operator", "value": "liumei"}, {"disabled": false, "key": "region", "value": "CN"}, {"key": "<PERSON><PERSON>", "value": "access_token=-EHPeW-UUYFLaLszRoCs1gGYOd_G0cxcXLwEDjuRzV5IeMfUXYDREJAOq0vYu2KcZoryHFll_hUCrq9Sg0T9kvIBE1gAW7aBjIVDrHUwmrmPKZygAckpVWdhY0TA4b37; refresh_token=RobdKgOWodqxZgf5SC14I-g7U7tBUt9_fTa6ULzjEpUA6B0VWx24Dg1b3fWsAp8_HE-YexhekYHUVrqZqmqlBXe0aMKJ62rQCzyKD4p5EzIQXids1HPjRmMLHKFTGO0i; d-locale=zh-CN"}, {"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}], "method": "GET", "baseUrl": "https://test-upmp.allsaints.group", "body": {}, "auth": {"type": "<PERSON><PERSON><PERSON>", "noauth": []}, "type": "http"}, "response": [], "event": [{"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        var jsonData = pm.response.json();", "", "//提取token", "var token = pm.variables.get(\"access_token\")", "var channel = pm.environment.get(\"channel\");", "", "//筛选响应体脏数据id", "var deleteDataId = jsonData.data.list.filter(item => item.content.includes('自动化')).filter(item => item.state != 4).map(item => item.id);", "", "//若未发现脏数据则退出", "if (deleteDataId.length == 0) {", "    console.log(\"未发现content含'自动化'字符串的脏数据\");", "    return;", "}", "", "for (i = 0; i < deleteDataId.length; i++) {", "    console.log(\"第\" + (i + 1) + \"个脏数据id是\" + deleteDataId[i]);", "}", "", "// 设置删除接口URL", "var deleteRequestUrl = \"https://test-upmp.allsaints.group/uss/api/admin/feedback/comment/delete\";", "", "pm.sendRequest({", "    url: deleteRequestUrl,", "    method: \"POST\",", "    header: {", "        \"Authorization\": \"Bearer \" + token,", "        \"Content-Type\": \"application/json\",", "        \"channel\": channel,", "        \"operator\": \"cmsAutoTest\",", "        \"region\": \"CN\"", "    },", "    body: {", "        mode: \"raw\",", "        raw: JSON.stringify({ ids: deleteDataId, commentType: 1, reason: \"删除自动化数据\" })", "    }", "}, (err, res) => {", "    if (err) {", "        console.error(\"删除失败:\", err);", "        return;", "    } else {", "        if (res.json().message == \"ok\") {", "            console.log(\"专辑评论脏数据删除完成\");", "        }", "        else {", "            pm.test(\"删除失败原因确认\", () => {", "                pm.expect.fail(`错误码: ${res.json().code}, 信息: ${res.json().message}`);", "            });", "        }", "    }", "});", "      "]}}], "responseDefinition": {"id": 579418700, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "52723c6f-a8a5-4395-abbb-8c5b1b2b86d2", "id": "96555d77-b43e-595c-b346-d930a385d133", "type": "http", "name": "删除专辑评论脏数据", "projectId": 4800107, "relatedId": 6669832, "environmentId": 24915297, "blockNumber": 3, "httpApiId": 241308714, "httpApiCaseId": 268234265, "httpApiName": "uss查询专辑评论", "httpApiPath": "/uss/api/admin/feedback/comment/album/page", "httpApiMethod": "get", "httpApiCaseName": "uss查询专辑评论"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "32d46b9c-f4ff-5923-9fec-ec7f703d4efc", "name": "USS-在app发送用户反馈", "request": {"url": {"protocol": "https", "path": ["api", "user", "feedback", "save"], "host": ["test-h5-oppo-mbi", "allsaints", "top"], "query": [], "variable": []}, "header": [{"disabled": false, "key": "Host", "value": "test-h5-oppo-mbi.allsaints.top"}, {"disabled": false, "key": "dev-package", "value": "com.heytap.music"}, {"disabled": false, "key": "dev-region", "value": "cn"}, {"disabled": false, "key": "dev-manufacturer", "value": "OPPO"}, {"disabled": false, "key": "dev-language", "value": "zh_cn"}, {"disabled": false, "key": "dev-timezone", "value": "Asia/Shanghai"}, {"disabled": false, "key": "uid", "value": "o_id_eEnWgHTJC4nQdZHi7mAQ9Q"}, {"disabled": false, "key": "dev-channel", "value": "{{channel}}"}, {"disabled": false, "key": "dev-build-version", "value": "40.10.16.60test_1c57bad_241014"}, {"disabled": false, "key": "dev-id", "value": "d41d8cd98f00b204e9800998ecf8427e"}, {"disabled": false, "key": "dev-distinct-id", "value": "6de5db3a-5eee-4c12-903b-595a4e287924"}, {"disabled": false, "key": "dev-app-version", "value": "101016060"}, {"disabled": false, "key": "second-type", "value": ""}, {"disabled": false, "key": "User-Agent", "value": "Mozilla/5.0 (Linux; Android 9; OPPO R11 Plus Build/NMF26X; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/92.0.4515.131 Mobile Safari/537.36 AllSaints_101016060"}, {"disabled": false, "key": "Accept", "value": "application/json, text/plain, */*"}, {"disabled": false, "key": "dev-pid", "value": "fdb5deea9bf44af083e3efc8a4d5ef6c"}, {"disabled": false, "key": "dev-model", "value": "OPPO R11 Plus"}, {"disabled": false, "key": "token", "value": "5578176d2a2b45c5845188202132b116"}, {"disabled": false, "key": "dev-brand", "value": "OPPO"}, {"disabled": false, "key": "Origin", "value": "https://test-oppo-h5.allsaints.top"}, {"disabled": false, "key": "X-Requested-With", "value": "com.heytap.music"}, {"disabled": false, "key": "Sec-Fetch-Site", "value": "same-site"}, {"disabled": false, "key": "Sec-Fetch-Mode", "value": "cors"}, {"disabled": false, "key": "Sec-Fetch-Dest", "value": "empty"}, {"disabled": false, "key": "Accept-Language", "value": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "https://test-h5-oppo-mbi.allsaints.top", "body": {"mode": "raw", "raw": "{\n    \"uid\": \"o_id_eEnWgHTJC4nQdZHi7mAQ9Q\",\n    \"utoken\": \"5578176d2a2b45c5845188202132b116\",\n    \"type\": 16,\n    \"mobile\": \"shgAkVvvPyN5W1guzgf8/V9X/HHEIx4b0W0FRWs6b7/7f7IBm56FhUQT/D5mUnLKq4puzu6zTk+f7HtHzXtKKcMVEZKhtBzHJtkP/DMmkew0Up8nn4Zz9zpJP/X7THugqofegTV2Io3Ldix3fB9aCUenLf83N7qIoyVWBa7qQ8zonzUgvZ70czZ0t7GQTkdHKjt1IMrKJ0hRTEp5wWpDgZlwoshAhJ6jP9e2fG4a8zG80LNxTGTbTddZilizZ/gsD6BRNk2kR04jhTd/gfjpYQ6wsP+iIM8rnnTVIRu3RazGhmwXawaV57OhRr8rguxMLopnJLBImGtsQAp6W2Dl1w==\",\n    \"imageType\": 1,\n    \"description\": \"自动化测试内容{{$number.int(min=1,max=9999)}}\",\n    \"fileName\": \"fdb5deea9bf44af083e3efc8a4d5ef6c_2024_11_13_13-2024_11_13_14_1731479371298.zip\",\n    \"imagePaths\": [\n        \"s3://allsaints-static-dev/app-sms/feedback/image/20241113/8ac354bd2a5746918f2ce334d9b6ce32.png\"\n    ],\n    \"buildVersion\": \"40.10.16.60test_1c57bad_241014\"\n}", "generateMode": "normal", "type": "application/json"}, "auth": {"type": "<PERSON><PERSON><PERSON>", "noauth": []}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`测试场景-在app进行用户反馈，查看是否成功反馈到USS后台并回复`);", "      pm.test(formattedName, async function(done) {", "        try {", "          const value = pm.response.text();", "          ", "    const compareValue = await ____replaceIn(\"\");", "    const formattedValues = ____formatValues(value, compareValue, 'exists');", "  ", "          pm.expectWithKey({key:'undefined',value: value === null ? '' : formattedValues.value }).to.exist", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        pm.test(\"返回的message为'ok'或者'你今天提交的次数已超过上限'\", function () {", "    const res = pm.response.json();", "    console.log(\"message:\" + res.message);", "    const mes1 = res.message === \"ok\";", "    const mes2 = res.message === \"你今天提交的次数已超过上限\";", "", "    pm.expect(mes1 || mes2).to.be.true;", "", "    if (res.message === \"ok\") {", "        var requestData = pm.request.body.raw;", "        if (!requestData) {", "            console.error(\"请求体内容为空\");", "            return;", "        }", "        try {", "            var jsonData = JSON.parse(requestData);", "            // 添加 description 字段检", "            if (jsonData.description) {", "                pm.variables.set(\"description\", jsonData.description);", "            } else {", "                console.error(\"JSON数据中缺少description字段\");", "            }", "        } catch (e) {", "            console.error(\"JSON解析失败:\", e);", "        }", "    }", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.2.httpApiExtractor", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        let requestJSO<PERSON>, responseJSON;", "      const extracts = [{\"expression\":\"$.1.response.body.message\",\"paths\":[\"$\",\"1\",\"response\",\"body\",\"message\"]}];", "      function getJSONPath(paths, index) {", "        const path = paths.slice(index).join('.');", "        const root = path[0] === '[' ? '$' : '$.';", "        return root + path;", "      }", "      function extractJSON(json, path) {", "        const JSONPath = require('jsonpath-plus').JSONPath;", "        return JSONPath({ json, path, wrap: false });", "      }", "      function getValue(propertyList, targetKey, keyName) {", "        keyName = keyName || 'key';", "        if (propertyList.filter) {", "          const values = propertyList.filter(item => item[keyName] === targetKey);", "          if (values.length === 1) {", "            return values[0].value;", "          } else if (values.length > 1) {", "            return values.map(item => item.value);", "          }", "        } else if (propertyList.get) {", "          return propertyList.get(targetKey);", "        }", "        return undefined;", "      }", "      ", "      for (const { expression, paths } of extracts) {", "        let value;", "        try {", "          if (paths[2] === 'request') {", "            if (paths[3] === 'url') {", "              value = pm.request.url.toString();", "            } else if (paths[3] === 'headers' || paths[3] === 'header') {", "              value = getValue(pm.request.headers, paths[4]);", "            } else if (paths[3] === 'pathParams' || paths[3] === 'pathParam') {", "              value = getValue(pm.request.url.variables, paths[4]);", "            } else if (paths[3] === 'query' || paths[3] === 'params' || paths[3] === 'param') {", "              value = getValue(pm.request.url.query, paths[4]);", "            } else if (paths[3] === 'body') {", "              const body = pm.request.body;", "              const bodyData = body[body.mode];", "              if (typeof bodyData === \"string\") {", "                if (!requestJSON) {", "                  requestJSON = JSON.parse(bodyData);", "                }", "                value = extractJSON(requestJSON, getJSONPath(paths, 4));", "              } else {", "                value = getValue(bodyData, paths[4]);", "                if (value === undefined) {", "                  value = extractJSON(body, getJSONPath(paths, 4));", "                }", "              }", "            } else if (paths[3] === 'cookies' || paths[3] === 'cookie') {", "              const cookies = pm.request.headers.get('<PERSON><PERSON>') || '';", "              if (paths[4]) {", "                cookies.split(';').find(cookie => {", "                  const splitIndex = cookie.indexOf('=');", "                  const cookieKey = cookie.slice(0, splitIndex);", "                  const cookieValue = cookie.slice(splitIndex + 1);", "                  if (cookieKey === paths[4]) {", "                    value = cookieValue;", "                    return true;", "                  }", "                })", "              } else {", "                value = cookies;", "              }", "            } else {", "              value = extractJSON(pm.request, getJSONPath(paths, 3));", "            }", "          } else if (paths[2] === 'response') {", "            if (paths[3] === 'headers' || paths[3] === 'header') {", "              value = getValue(pm.response.headers, paths[4]);", "            } else if (paths[3] === 'cookies' || paths[3] === 'cookie') {", "              value = getValue(pm.response.cookies, paths[4], 'name');", "            } else if (paths[3] === 'body') {", "              if (!responseJSON) {", "                responseJSON = pm.response.json();", "              }", "              value = extractJSON(responseJSON, getJSONPath(paths, 4));", "            } else {", "              value = extractJSON(pm.response, getJSONPath(paths, 3));", "            }", "          } ", "", "          if (value === null) {", "            value = 'null'", "          }", "", "          pm.variables.set(expression, value);", "        } catch (e) {", "          console.log('httpApiExtractorScriptError', e)", "        }", "      }", "      "]}}], "responseDefinition": {"id": 563866966, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "oasExtensions": "", "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"uid": {"type": "string"}, "utoken": {"type": "string"}, "type": {"type": "integer"}, "mobile": {"type": "string"}, "imageType": {"type": "integer"}, "description": {"type": "string"}, "fileName": {"type": "string"}, "imagePaths": {"type": "array", "items": {"type": "string"}}, "buildVersion": {"type": "string"}}, "required": ["uid", "utoken", "type", "mobile", "imageType", "description", "fileName", "imagePaths", "buildVersion"]}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "4e482c07-de1c-4dc8-8bda-b0e4058b0f1d", "id": "32d46b9c-f4ff-5923-9fec-ec7f703d4efc", "type": "http", "name": "USS-在app发送用户反馈", "projectId": 4800107, "relatedId": 6669829, "environmentId": 24915297, "blockNumber": 1, "httpApiId": 233154038, "httpApiCaseId": 275592191, "httpApiName": "app用户反馈", "httpApiPath": "/api/user/feedback/save", "httpApiMethod": "post", "httpApiCaseName": "app用户反馈"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "8a95924b-29b6-51b4-bdf8-3779f6ba8917", "type": "if", "metaInfo": {"type": "if", "scopeType": "start", "scopeEndId": "6423a1bd-480c-5031-9488-a989bc7f4ff2", "events": [{"description": "{{$.1.response.body.message}} equal ok", "listen": "test", "script": {"exec": "\n\n  ____string2Array = function(value) {\n    if(typeof value === 'object'){\n      return value;\n    }\n    try {\n      return JSON.parse(value);\n    } catch (e) {\n      return value;\n    }\n  }\n  ____string2Number = function(value, errorMsg) {\n   if(typeof value !== 'string'){\n     return value;\n   }\n   if (/^\\-?\\d+$/.test(value)) {\n       return parseInt(value);\n   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {\n       return parseFloat(value);\n   } else {\n       throw new Error(errorMsg || '数据类型不匹配')\n   }\n }\n\n  ____formatValues = function(value, stringCompareValue, comparison) {\n   try{\n     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);\n     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);\n     let compareValue;\n     switch (typeof value) {\n         case 'string':\n             if (isNumberComparisons) {\n                value = ____string2Number(value);\n                compareValue = ____string2Number(stringCompareValue);\n             } else if (isCollectionComparisons) {\n                compareValue = ____string2Array(stringCompareValue);\n             } else if (comparison === 'exists' || comparison === 'notExist') {\n                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;\n             } else {\n                compareValue = stringCompareValue;\n             }\n             break;\n         case 'object':\n             const isArray = value instanceof Array;\n             if (isNumberComparisons) {\n                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')\n             } else if (isCollectionComparisons && isArray) {\n              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')\n            } else if (\n              isArray &&\n              comparison === 'include' &&\n              value.includes(stringCompareValue)\n            ) {\n              compareValue = stringCompareValue;\n            } else {\n              try {\n                  compareValue = JSON.parse(stringCompareValue);\n              } catch (e) {\n                  compareValue = stringCompareValue;\n              }\n            }\n             break;\n         case 'boolean':\n             if (isNumberComparisons || isCollectionComparisons) {\n                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')\n             }\n             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);\n             break;\n           case 'bigint':\n           case 'number':\n             if (isNumberComparisons) {\n               compareValue = ____string2Number(stringCompareValue);\n             } else if (isCollectionComparisons) {\n              compareValue = ____string2Array(stringCompareValue);\n              value = '' + value;\n            } else {\n               compareValue = stringCompareValue;\n               value = '' + value;\n             }\n             break;\n         case 'null':\n             if (isNumberComparisons) {\n                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')\n             }\n             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;\n             break;\n         default:\n            if (isCollectionComparisons) {\n              compareValue = ____string2Array(stringCompareValue);\n            } else {\n              compareValue = stringCompareValue;\n            }\n            break;\n     }\n     return { compareValue, value };\n   } catch(e) {\n     console.log(e);\n     throw e;\n   }\n }\n \n\n        if (JSON.setEnableBigInt) {\n          JSON.setEnableBigInt(undefined);\n        }\n        \n  async function ____replaceIn(value) {\n    if (typeof pm.variables.replaceInAsync === 'function') {\n      return await pm.variables.replaceInAsync(value);\n    }\n    return pm.variables.replaceIn(value);\n  };\n\n  ;(async () => {\n    try {\n      const formattedName = await ____replaceIn(`undefined 等于 ok`);\n      pm.test(formattedName, async function(done) {\n        try {\n          const value = await ____replaceIn(`{{$.1.response.body.message}}`);\n          \n    const compareValue = await ____replaceIn(`ok`);\n    const formattedValues = ____formatValues(value, compareValue, 'equal');\n  \n          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);\n          done();\n        } catch (err) {\n          done(err);\n        }\n      });\n    } catch(e) {\n      setImmediate(() => { throw e });\n    }\n  })();\n      ", "id": "if.0.assertion", "type": "text/javascript"}}]}}, {"id": "da5174fe-6268-5429-9d40-f4c76eaa7cfb", "type": "if", "metaInfo": {"type": "if", "scopeType": "start", "scopeEndId": "1d55155f-2a76-5ccb-a8dd-1f04ac14fcac", "events": [{"description": "{{description}} notEqual undefined", "listen": "test", "script": {"exec": "\n\n  ____string2Array = function(value) {\n    if(typeof value === 'object'){\n      return value;\n    }\n    try {\n      return JSON.parse(value);\n    } catch (e) {\n      return value;\n    }\n  }\n  ____string2Number = function(value, errorMsg) {\n   if(typeof value !== 'string'){\n     return value;\n   }\n   if (/^\\-?\\d+$/.test(value)) {\n       return parseInt(value);\n   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {\n       return parseFloat(value);\n   } else {\n       throw new Error(errorMsg || '数据类型不匹配')\n   }\n }\n\n  ____formatValues = function(value, stringCompareValue, comparison) {\n   try{\n     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);\n     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);\n     let compareValue;\n     switch (typeof value) {\n         case 'string':\n             if (isNumberComparisons) {\n                value = ____string2Number(value);\n                compareValue = ____string2Number(stringCompareValue);\n             } else if (isCollectionComparisons) {\n                compareValue = ____string2Array(stringCompareValue);\n             } else if (comparison === 'exists' || comparison === 'notExist') {\n                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;\n             } else {\n                compareValue = stringCompareValue;\n             }\n             break;\n         case 'object':\n             const isArray = value instanceof Array;\n             if (isNumberComparisons) {\n                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')\n             } else if (isCollectionComparisons && isArray) {\n              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')\n            } else if (\n              isArray &&\n              comparison === 'include' &&\n              value.includes(stringCompareValue)\n            ) {\n              compareValue = stringCompareValue;\n            } else {\n              try {\n                  compareValue = JSON.parse(stringCompareValue);\n              } catch (e) {\n                  compareValue = stringCompareValue;\n              }\n            }\n             break;\n         case 'boolean':\n             if (isNumberComparisons || isCollectionComparisons) {\n                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')\n             }\n             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);\n             break;\n           case 'bigint':\n           case 'number':\n             if (isNumberComparisons) {\n               compareValue = ____string2Number(stringCompareValue);\n             } else if (isCollectionComparisons) {\n              compareValue = ____string2Array(stringCompareValue);\n              value = '' + value;\n            } else {\n               compareValue = stringCompareValue;\n               value = '' + value;\n             }\n             break;\n         case 'null':\n             if (isNumberComparisons) {\n                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')\n             }\n             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;\n             break;\n         default:\n            if (isCollectionComparisons) {\n              compareValue = ____string2Array(stringCompareValue);\n            } else {\n              compareValue = stringCompareValue;\n            }\n            break;\n     }\n     return { compareValue, value };\n   } catch(e) {\n     console.log(e);\n     throw e;\n   }\n }\n \n\n        if (JSON.setEnableBigInt) {\n          JSON.setEnableBigInt(undefined);\n        }\n        \n  async function ____replaceIn(value) {\n    if (typeof pm.variables.replaceInAsync === 'function') {\n      return await pm.variables.replaceInAsync(value);\n    }\n    return pm.variables.replaceIn(value);\n  };\n\n  ;(async () => {\n    try {\n      const formattedName = await ____replaceIn(`undefined 不等于 undefined`);\n      pm.test(formattedName, async function(done) {\n        try {\n          const value = await ____replaceIn(`{{description}}`);\n          \n    const compareValue = await ____replaceIn(`undefined`);\n    const formattedValues = ____formatValues(value, compareValue, 'notEqual');\n  \n          pm.expect(formattedValues.value).to.not.eql(formattedValues.compareValue);\n          done();\n        } catch (err) {\n          done(err);\n        }\n      });\n    } catch(e) {\n      setImmediate(() => { throw e });\n    }\n  })();\n      ", "id": "if.0.assertion", "type": "text/javascript"}}]}}, {"id": "b12f843b-4e8f-58b0-ab6e-680f08d7c528", "type": "delay", "metaInfo": {"id": "b12f843b-4e8f-58b0-ab6e-680f08d7c528", "type": "delay", "timeout": 2000}}, {"id": "cd5a8fc7-1ab5-52cf-90eb-adc0941f9ee4", "name": "USS-获取token", "request": {"url": {"protocol": "https", "path": ["<PERSON><PERSON><PERSON><PERSON>", "auth", "oauth2", "token"], "host": ["test-upmp", "allsaints", "group"], "query": [{"disabled": false, "key": "t", "value": "{{$date.millisecondsTimestamp}}"}, {"disabled": false, "key": "scope", "value": "server"}, {"disabled": false, "key": "grant_type", "value": "password"}, {"disabled": false, "key": "username", "value": "{{username}}"}], "variable": []}, "header": [{"disabled": false, "key": "authorization", "value": "Basic dXBtcDozUllyclNROGtjQWdGOGVOa3owelZ3bz0="}, {"key": "<PERSON><PERSON>", "value": "d-locale=zh-CN"}, {"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}], "method": "POST", "baseUrl": "https://test-upmp.allsaints.group", "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"disabled": false, "key": "password", "value": "{{password}}"}]}, "auth": {"type": "<PERSON><PERSON><PERSON>", "noauth": []}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`测试场景-在app进行用户反馈，查看是否成功反馈到USS后台并回复`);", "      pm.test(formattedName, async function(done) {", "        try {", "          const value = pm.response.text();", "          ", "    const compareValue = await ____replaceIn(\"\");", "    const formattedValues = ____formatValues(value, compareValue, 'exists');", "  ", "          pm.expectWithKey({key:'undefined',value: value === null ? '' : formattedValues.value }).to.exist", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.code 等于 0000`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.code`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`0000`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.2.extractor", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "      async function ____replaceIn(value) {", "        if (typeof pm.variables.replaceInAsync === 'function') {", "          return await pm.variables.replaceInAsync(value);", "        }", "        return pm.variables.replaceIn(value);", "      };", "      ;(async () => {", "        try{", "          ", "          const expression = pm.variables.replaceIn(`$.access_token`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          const jsonData = pm.response.json();", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "          ", "          ", "        switch (typeof value) {", "          case 'object':", "            value = JSON.stringify(value);", "            break;", "          default:", "            value = String(value);", "            break;", "        }", "      ", "          const formattedName = await ____replaceIn(`access_token`);pm.variables.set(formattedName, value);console.log('___label_placeholder__processor___', '已设置临时变量【' + formattedName + '】，值为' + ' ' + '【' + value + '】')", "        } catch(e) {", "          e.message = `提取变量【access_token】出错: ` + e.message;", "          setImmediate(() => { throw e });", "        }", "      })();", "      "]}}], "responseDefinition": {"id": 486870571, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"sub": {"type": "string"}, "clientId": {"type": "string"}, "code": {"type": "string"}, "iss": {"type": "string"}, "token_type": {"type": "string"}, "access_token": {"type": "string"}, "refresh_token": {"type": "string"}, "aud": {"type": "array", "items": {"type": "string"}}, "license": {"type": "string"}, "nbf": {"type": "string"}, "user_info": {"type": "object", "properties": {"username": {"type": "string"}, "authorities": {"type": "array", "items": {"type": "object", "properties": {"role": {"type": "string"}}, "required": ["role"]}}, "accountNonExpired": {"type": "boolean"}, "accountNonLocked": {"type": "boolean"}, "credentialsNonExpired": {"type": "boolean"}, "enabled": {"type": "boolean"}, "attributes": {"type": "object", "properties": {}}, "id": {"type": "string"}, "deptId": {"type": "string"}, "email": {"type": "string"}, "mfaEnabled": {"type": "boolean"}, "mfaSecret": {"type": "string"}, "nickname": {"type": "string"}}, "required": ["username", "authorities", "accountNonExpired", "accountNonLocked", "credentialsNonExpired", "enabled", "attributes", "id", "deptId", "email", "mfaEnabled", "mfaSecret", "nickname"]}, "user_id": {"type": "integer"}, "needBinding": {"type": "boolean"}, "scope": {"type": "array", "items": {"type": "string"}}, "exp": {"type": "string"}, "expires_in": {"type": "integer"}, "iat": {"type": "string"}, "jti": {"type": "string"}, "username": {"type": "string"}}, "required": ["sub", "clientId", "code", "iss", "token_type", "access_token", "refresh_token", "aud", "license", "nbf", "user_info", "user_id", "needBinding", "scope", "exp", "expires_in", "iat", "jti", "username"]}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "oasExtensions": "", "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "8bd95c51-59d4-471d-91bb-3da08fc0e9b4", "id": "cd5a8fc7-1ab5-52cf-90eb-adc0941f9ee4", "type": "http", "name": "USS-获取token", "projectId": 4800107, "relatedId": 6669829, "environmentId": 24915297, "blockNumber": 5, "httpApiId": 192391555, "httpApiCaseId": 275592193, "httpApiName": "获取token", "httpApiPath": "/upmpsapi/auth/oauth2/token", "httpApiMethod": "post", "httpApiCaseName": "获取token"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "8e647486-0c25-5ea5-a839-de1d1e46deff", "name": "USS-获取uss用户反馈列表，提取变量", "request": {"url": {"protocol": "https", "path": ["uss", "api", "admin", "feedback", "list"], "host": ["test-upmp", "allsaints", "group"], "query": [{"disabled": false, "key": "page", "value": "1"}, {"disabled": false, "key": "size", "value": "10"}, {"disabled": false, "key": "handleStatus", "value": "0"}], "variable": []}, "header": [{"disabled": false, "key": "Accept", "value": "application/json, text/plain, */*"}, {"disabled": false, "key": "Accept-Language", "value": "zh-CN,zh;q=0.9,en;q=0.8"}, {"disabled": false, "key": "Authorization", "value": "Bearer {{access_token}}"}, {"disabled": false, "key": "Cache-Control", "value": "no-cache"}, {"disabled": false, "key": "Connection", "value": "keep-alive"}, {"disabled": false, "key": "Pragma", "value": "no-cache"}, {"disabled": false, "key": "<PERSON><PERSON><PERSON>", "value": "https://test-upmp.allsaints.group/USS/feed/back"}, {"disabled": false, "key": "Sec-Fetch-Dest", "value": "empty"}, {"disabled": false, "key": "Sec-Fetch-Mode", "value": "cors"}, {"disabled": false, "key": "Sec-Fetch-Site", "value": "same-origin"}, {"disabled": false, "key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36"}, {"disabled": false, "key": "channel", "value": "{{channel}}"}, {"disabled": false, "key": "operator", "value": "qiuweidong"}, {"disabled": false, "key": "region", "value": "CN"}, {"disabled": false, "key": "sec-ch-ua", "value": "\"Chromium\";v=\"130\", \"Google Chrome\";v=\"130\", \"Not?A_Brand\";v=\"99\""}, {"disabled": false, "key": "sec-ch-ua-mobile", "value": "?0"}, {"disabled": false, "key": "sec-ch-ua-platform", "value": "\"Windows\""}, {"key": "<PERSON><PERSON>", "value": "access_token={{access_token}}; refresh_token={{access_token}}; d-locale=zh-CN"}, {"key": "Content-Type", "value": "application/json"}], "method": "GET", "baseUrl": "https://test-upmp.allsaints.group", "body": {"mode": "raw", "raw": ""}, "auth": {"type": "<PERSON><PERSON><PERSON>", "noauth": []}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`测试场景-在app进行用户反馈，查看是否成功反馈到USS后台并回复`);", "      pm.test(formattedName, async function(done) {", "        try {", "          const value = pm.response.text();", "          ", "    const compareValue = await ____replaceIn(\"\");", "    const formattedValues = ____formatValues(value, compareValue, 'exists');", "  ", "          pm.expectWithKey({key:'undefined',value: value === null ? '' : formattedValues.value }).to.exist", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.code 等于 0000`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.code`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`0000`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.2.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.message 等于 ok`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.message`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`ok`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.3.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`字段description存在`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data.content[?(@.description==\"{{description}}\")]`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`测试内容，可以忽略`);", "    const formattedValues = ____formatValues(value, compareValue, 'exists');", "  ", "          pm.expectWithKey({key:'$.data.content[?(@.description==\"{{description}}\")]',value: value === null ? '' : formattedValues.value }).to.exist", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.4.extractor", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "      async function ____replaceIn(value) {", "        if (typeof pm.variables.replaceInAsync === 'function') {", "          return await pm.variables.replaceInAsync(value);", "        }", "        return pm.variables.replaceIn(value);", "      };", "      ;(async () => {", "        try{", "          ", "          const expression = pm.variables.replaceIn(`$.data.content[?(@.description==\"{{description}}\")].id`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          const jsonData = pm.response.json();", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "", "          if (true && 0 !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(0) >= 0 ? value[0] : value[value.length + Number(0)];", "            } else {", "              value = undefined;", "            }", "          }", "          ", "          ", "        switch (typeof value) {", "          case 'object':", "            value = JSON.stringify(value);", "            break;", "          default:", "            value = String(value);", "            break;", "        }", "      ", "          const formattedName = await ____replaceIn(`id`);pm.variables.set(formattedName, value);console.log('___label_placeholder__processor___', '已设置临时变量【' + formattedName + '】，值为' + ' ' + '【' + value + '】')", "        } catch(e) {", "          e.message = `提取变量【id】出错: ` + e.message;", "          setImmediate(() => { throw e });", "        }", "      })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.5.extractor", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "      async function ____replaceIn(value) {", "        if (typeof pm.variables.replaceInAsync === 'function') {", "          return await pm.variables.replaceInAsync(value);", "        }", "        return pm.variables.replaceIn(value);", "      };", "      ;(async () => {", "        try{", "          ", "          const expression = pm.variables.replaceIn(`$.data.content[?(@.description==\"{{description}}\")].uid`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          const jsonData = pm.response.json();", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "", "          if (true && 0 !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(0) >= 0 ? value[0] : value[value.length + Number(0)];", "            } else {", "              value = undefined;", "            }", "          }", "          ", "          ", "        switch (typeof value) {", "          case 'object':", "            value = JSON.stringify(value);", "            break;", "          default:", "            value = String(value);", "            break;", "        }", "      ", "          const formattedName = await ____replaceIn(`uid`);pm.variables.set(formattedName, value);console.log('___label_placeholder__processor___', '已设置临时变量【' + formattedName + '】，值为' + ' ' + '【' + value + '】')", "        } catch(e) {", "          e.message = `提取变量【uid】出错: ` + e.message;", "          setImmediate(() => { throw e });", "        }", "      })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.6.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`id 存在 `);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`id`);", "          const value = pm.variables.get(expression)", "      ", "          ", "    const compareValue = await ____replaceIn(\"\");", "    const formattedValues = ____formatValues(value, compareValue, 'exists');", "  ", "          pm.expectWithKey({key:'id',value: value === null ? '' : formattedValues.value }).to.exist", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.7.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`uid 存在 `);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`uid`);", "          const value = pm.variables.get(expression)", "      ", "          ", "    const compareValue = await ____replaceIn(\"\");", "    const formattedValues = ____formatValues(value, compareValue, 'exists');", "  ", "          pm.expectWithKey({key:'uid',value: value === null ? '' : formattedValues.value }).to.exist", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}], "responseDefinition": {"id": 563972504, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "b1d01502-a6c6-4506-8058-ead53b0db28f", "id": "8e647486-0c25-5ea5-a839-de1d1e46deff", "type": "http", "name": "USS-获取uss用户反馈列表，提取变量", "projectId": 4800107, "relatedId": 6669829, "environmentId": 24915297, "blockNumber": 6, "httpApiId": 233214269, "httpApiCaseId": 275592192, "httpApiName": "uss用户反馈列表", "httpApiPath": "/uss/api/admin/feedback/list", "httpApiMethod": "get", "httpApiCaseName": "uss用户反馈列表"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "fad4d1d5-12b5-5c23-ba73-ae72e7edd31a", "type": "if", "metaInfo": {"type": "if", "scopeType": "start", "scopeEndId": "ed4a1f5b-2464-56fe-8b01-a15259e3e240", "events": [{"description": "{{id}} notEqual undefined", "listen": "test", "script": {"exec": "\n\n  ____string2Array = function(value) {\n    if(typeof value === 'object'){\n      return value;\n    }\n    try {\n      return JSON.parse(value);\n    } catch (e) {\n      return value;\n    }\n  }\n  ____string2Number = function(value, errorMsg) {\n   if(typeof value !== 'string'){\n     return value;\n   }\n   if (/^\\-?\\d+$/.test(value)) {\n       return parseInt(value);\n   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {\n       return parseFloat(value);\n   } else {\n       throw new Error(errorMsg || '数据类型不匹配')\n   }\n }\n\n  ____formatValues = function(value, stringCompareValue, comparison) {\n   try{\n     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);\n     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);\n     let compareValue;\n     switch (typeof value) {\n         case 'string':\n             if (isNumberComparisons) {\n                value = ____string2Number(value);\n                compareValue = ____string2Number(stringCompareValue);\n             } else if (isCollectionComparisons) {\n                compareValue = ____string2Array(stringCompareValue);\n             } else if (comparison === 'exists' || comparison === 'notExist') {\n                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;\n             } else {\n                compareValue = stringCompareValue;\n             }\n             break;\n         case 'object':\n             const isArray = value instanceof Array;\n             if (isNumberComparisons) {\n                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')\n             } else if (isCollectionComparisons && isArray) {\n              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')\n            } else if (\n              isArray &&\n              comparison === 'include' &&\n              value.includes(stringCompareValue)\n            ) {\n              compareValue = stringCompareValue;\n            } else {\n              try {\n                  compareValue = JSON.parse(stringCompareValue);\n              } catch (e) {\n                  compareValue = stringCompareValue;\n              }\n            }\n             break;\n         case 'boolean':\n             if (isNumberComparisons || isCollectionComparisons) {\n                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')\n             }\n             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);\n             break;\n           case 'bigint':\n           case 'number':\n             if (isNumberComparisons) {\n               compareValue = ____string2Number(stringCompareValue);\n             } else if (isCollectionComparisons) {\n              compareValue = ____string2Array(stringCompareValue);\n              value = '' + value;\n            } else {\n               compareValue = stringCompareValue;\n               value = '' + value;\n             }\n             break;\n         case 'null':\n             if (isNumberComparisons) {\n                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')\n             }\n             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;\n             break;\n         default:\n            if (isCollectionComparisons) {\n              compareValue = ____string2Array(stringCompareValue);\n            } else {\n              compareValue = stringCompareValue;\n            }\n            break;\n     }\n     return { compareValue, value };\n   } catch(e) {\n     console.log(e);\n     throw e;\n   }\n }\n \n\n        if (JSON.setEnableBigInt) {\n          JSON.setEnableBigInt(undefined);\n        }\n        \n  async function ____replaceIn(value) {\n    if (typeof pm.variables.replaceInAsync === 'function') {\n      return await pm.variables.replaceInAsync(value);\n    }\n    return pm.variables.replaceIn(value);\n  };\n\n  ;(async () => {\n    try {\n      const formattedName = await ____replaceIn(`undefined 不等于 undefined`);\n      pm.test(formattedName, async function(done) {\n        try {\n          const value = await ____replaceIn(`{{id}}`);\n          \n    const compareValue = await ____replaceIn(`undefined`);\n    const formattedValues = ____formatValues(value, compareValue, 'notEqual');\n  \n          pm.expect(formattedValues.value).to.not.eql(formattedValues.compareValue);\n          done();\n        } catch (err) {\n          done(err);\n        }\n      });\n    } catch(e) {\n      setImmediate(() => { throw e });\n    }\n  })();\n      ", "id": "if.0.assertion", "type": "text/javascript"}}]}}, {"id": "70fd891e-e0c2-5ef5-970e-91b433e7d267", "name": "USS-查看uss用户上传日志判断logfile是否存在", "request": {"url": {"protocol": "https", "path": ["uss", "api", "admin", "feedback", "info"], "host": ["test-upmp", "allsaints", "group"], "query": [{"disabled": false, "key": "id", "value": "{{id}}"}], "variable": []}, "header": [{"disabled": false, "key": "Accept", "value": "application/json, text/plain, */*"}, {"disabled": false, "key": "Accept-Language", "value": "zh-CN,zh;q=0.9,en;q=0.8"}, {"disabled": false, "key": "Authorization", "value": "Bearer {{access_token}}"}, {"disabled": false, "key": "Cache-Control", "value": "no-cache"}, {"disabled": false, "key": "Connection", "value": "keep-alive"}, {"disabled": false, "key": "Pragma", "value": "no-cache"}, {"disabled": false, "key": "<PERSON><PERSON><PERSON>", "value": "https://test-upmp.allsaints.group/USS/feed/back"}, {"disabled": false, "key": "Sec-Fetch-Dest", "value": "empty"}, {"disabled": false, "key": "Sec-Fetch-Mode", "value": "cors"}, {"disabled": false, "key": "Sec-Fetch-Site", "value": "same-origin"}, {"disabled": false, "key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36"}, {"disabled": false, "key": "channel", "value": "{{channel}}"}, {"disabled": false, "key": "operator", "value": "qiuweidong"}, {"disabled": false, "key": "region", "value": "CN"}, {"disabled": false, "key": "sec-ch-ua", "value": "\"Chromium\";v=\"130\", \"Google Chrome\";v=\"130\", \"Not?A_Brand\";v=\"99\""}, {"disabled": false, "key": "sec-ch-ua-mobile", "value": "?0"}, {"disabled": false, "key": "sec-ch-ua-platform", "value": "\"Windows\""}, {"key": "<PERSON><PERSON>", "value": "access_token={{access_token}}; refresh_token={{access_token}}; d-locale=zh-CN"}, {"key": "Content-Type", "value": "application/json"}], "method": "GET", "baseUrl": "https://test-upmp.allsaints.group", "body": {"mode": "raw", "raw": ""}, "auth": {"type": "<PERSON><PERSON><PERSON>", "noauth": []}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`测试场景-在app进行用户反馈，查看是否成功反馈到USS后台并回复`);", "      pm.test(formattedName, async function(done) {", "        try {", "          const value = pm.response.text();", "          ", "    const compareValue = await ____replaceIn(\"\");", "    const formattedValues = ____formatValues(value, compareValue, 'exists');", "  ", "          pm.expectWithKey({key:'undefined',value: value === null ? '' : formattedValues.value }).to.exist", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.code 等于 0000`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.code`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`0000`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.2.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.message 等于 ok`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.message`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`ok`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.3.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`logfile存在`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data.filePath`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(\"\");", "    const formattedValues = ____formatValues(value, compareValue, 'exists');", "  ", "          pm.expectWithKey({key:'$.data.filePath',value: value === null ? '' : formattedValues.value }).to.exist", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}], "responseDefinition": {"id": 563972113, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "cfaec1f3-541c-4555-b4d0-ee57a4903131", "id": "70fd891e-e0c2-5ef5-970e-91b433e7d267", "type": "http", "name": "USS-查看uss用户上传日志判断logfile是否存在", "projectId": 4800107, "relatedId": 6669829, "environmentId": 24915297, "blockNumber": 8, "httpApiId": 233218652, "httpApiCaseId": 275592196, "httpApiName": "uss用户反馈详情页", "httpApiPath": "/uss/api/admin/feedback/info", "httpApiMethod": "get", "httpApiCaseName": "uss用户反馈详情页"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "b10544c9-9fcf-5ef0-ac40-3e3a77ea5806", "name": "USS-在USS对用户反馈进行回复消息", "request": {"url": {"protocol": "https", "path": ["uss", "api", "admin", "reply", "answer"], "host": ["test-upmp", "allsaints", "group"], "query": [], "variable": []}, "header": [{"disabled": false, "key": "Accept", "value": "application/json, text/plain, */*"}, {"disabled": false, "key": "Accept-Language", "value": "zh-CN,zh;q=0.9,en;q=0.8"}, {"disabled": false, "key": "Authorization", "value": "Bearer {{access_token}}"}, {"disabled": false, "key": "Cache-Control", "value": "no-cache"}, {"disabled": false, "key": "Connection", "value": "keep-alive"}, {"disabled": false, "key": "Origin", "value": "https://test-upmp.allsaints.group"}, {"disabled": false, "key": "Pragma", "value": "no-cache"}, {"disabled": false, "key": "<PERSON><PERSON><PERSON>", "value": "https://test-upmp.allsaints.group/USS/feed/back"}, {"disabled": false, "key": "Sec-Fetch-Dest", "value": "empty"}, {"disabled": false, "key": "Sec-Fetch-Mode", "value": "cors"}, {"disabled": false, "key": "Sec-Fetch-Site", "value": "same-origin"}, {"disabled": false, "key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36"}, {"disabled": false, "key": "channel", "value": "{{channel}}"}, {"disabled": false, "key": "operator", "value": "qiuweidong"}, {"disabled": false, "key": "region", "value": "CN"}, {"disabled": false, "key": "sec-ch-ua", "value": "\"Chromium\";v=\"130\", \"Google Chrome\";v=\"130\", \"Not?A_Brand\";v=\"99\""}, {"disabled": false, "key": "sec-ch-ua-mobile", "value": "?0"}, {"disabled": false, "key": "sec-ch-ua-platform", "value": "\"Windows\""}, {"key": "<PERSON><PERSON>", "value": "access_token={{access_token}}; refresh_token={{access_token}}; d-locale=zh-CN"}], "method": "POST", "baseUrl": "https://test-upmp.allsaints.group", "body": {"mode": "formdata", "formdata": [{"disabled": false, "key": "uid", "value": "{{uid}}", "type": "string"}, {"disabled": false, "key": "feedbackId", "value": "{{id}}", "type": "string"}, {"disabled": false, "key": "msg", "value": "自动化回复内容{{$number.int(min=1,max=9999)}}", "type": "string"}, {"disabled": false, "key": "operator", "value": "qiuweidong", "type": "string"}]}, "auth": {"type": "<PERSON><PERSON><PERSON>", "noauth": []}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`测试场景-在app进行用户反馈，查看是否成功反馈到USS后台并回复`);", "      pm.test(formattedName, async function(done) {", "        try {", "          const value = pm.response.text();", "          ", "    const compareValue = await ____replaceIn(\"\");", "    const formattedValues = ____formatValues(value, compareValue, 'exists');", "  ", "          pm.expectWithKey({key:'undefined',value: value === null ? '' : formattedValues.value }).to.exist", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.code 等于 0000`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.code`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`0000`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.2.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.message 等于 发送成功`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.message`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`发送成功`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}], "responseDefinition": {"id": 564126933, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "c55ce558-4b46-402f-b8cc-17721d5e077a", "id": "b10544c9-9fcf-5ef0-ac40-3e3a77ea5806", "type": "http", "name": "USS-在USS对用户反馈进行回复消息", "projectId": 4800107, "relatedId": 6669829, "environmentId": 24915297, "blockNumber": 9, "httpApiId": 233294367, "httpApiCaseId": 275592195, "httpApiName": "uss回复消息", "httpApiPath": "/uss/api/admin/reply/answer", "httpApiMethod": "post", "httpApiCaseName": "uss回复消息"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "eac74974-7f6b-5bc0-8388-8e88d6c766e8", "type": "delay", "metaInfo": {"id": "eac74974-7f6b-5bc0-8388-8e88d6c766e8", "type": "delay", "timeout": 1000}}, {"id": "12d95c26-5083-5e4f-8d32-a0db3c6ff0b0", "name": "USS-查看回复用户弹窗内容，提取变量", "request": {"url": {"protocol": "https", "path": ["uss", "api", "admin", "reply", "list"], "host": ["test-upmp", "allsaints", "group"], "query": [{"disabled": false, "key": "feedbackId", "value": "{{id}}"}, {"disabled": false, "key": "size", "value": "999"}], "variable": []}, "header": [{"disabled": false, "key": "Accept", "value": "application/json, text/plain, */*"}, {"disabled": false, "key": "Accept-Language", "value": "zh-CN,zh;q=0.9,en;q=0.8"}, {"disabled": false, "key": "Authorization", "value": "Bearer {{access_token}}"}, {"disabled": false, "key": "Cache-Control", "value": "no-cache"}, {"disabled": false, "key": "Connection", "value": "keep-alive"}, {"disabled": false, "key": "Pragma", "value": "no-cache"}, {"disabled": false, "key": "<PERSON><PERSON><PERSON>", "value": "https://test-upmp.allsaints.group/USS/feed/back"}, {"disabled": false, "key": "Sec-Fetch-Dest", "value": "empty"}, {"disabled": false, "key": "Sec-Fetch-Mode", "value": "cors"}, {"disabled": false, "key": "Sec-Fetch-Site", "value": "same-origin"}, {"disabled": false, "key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36"}, {"disabled": false, "key": "channel", "value": "{{channel}}"}, {"disabled": false, "key": "operator", "value": "qiuweidong"}, {"disabled": false, "key": "region", "value": "CN"}, {"disabled": false, "key": "sec-ch-ua", "value": "\"Chromium\";v=\"130\", \"Google Chrome\";v=\"130\", \"Not?A_Brand\";v=\"99\""}, {"disabled": false, "key": "sec-ch-ua-mobile", "value": "?0"}, {"disabled": false, "key": "sec-ch-ua-platform", "value": "\"Windows\""}, {"key": "<PERSON><PERSON>", "value": "access_token={{access_token}}; refresh_token={{access_token}}; d-locale=zh-CN"}], "method": "GET", "baseUrl": "https://test-upmp.allsaints.group", "body": {}, "auth": {"type": "<PERSON><PERSON><PERSON>", "noauth": []}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`测试场景-在app进行用户反馈，查看是否成功反馈到USS后台并回复`);", "      pm.test(formattedName, async function(done) {", "        try {", "          const value = pm.response.text();", "          ", "    const compareValue = await ____replaceIn(\"\");", "    const formattedValues = ____formatValues(value, compareValue, 'exists');", "  ", "          pm.expectWithKey({key:'undefined',value: value === null ? '' : formattedValues.value }).to.exist", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.code 等于 0000`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.code`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`0000`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.2.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.message 等于 ok`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.message`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`ok`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.3.extractor", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "      async function ____replaceIn(value) {", "        if (typeof pm.variables.replaceInAsync === 'function') {", "          return await pm.variables.replaceInAsync(value);", "        }", "        return pm.variables.replaceIn(value);", "      };", "      ;(async () => {", "        try{", "          ", "          const expression = pm.variables.replaceIn(`$.data.content[*].msg`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          const jsonData = pm.response.json();", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "", "          if (true && -1 !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(-1) >= 0 ? value[-1] : value[value.length + Number(-1)];", "            } else {", "              value = undefined;", "            }", "          }", "          ", "          ", "        switch (typeof value) {", "          case 'object':", "            value = JSON.stringify(value);", "            break;", "          default:", "            value = String(value);", "            break;", "        }", "      ", "          const formattedName = await ____replaceIn(`uss_reply_msg`);pm.variables.set(formattedName, value);console.log('___label_placeholder__processor___', '已设置临时变量【' + formattedName + '】，值为' + ' ' + '【' + value + '】')", "        } catch(e) {", "          e.message = `提取变量【uss_reply_msg】出错: ` + e.message;", "          setImmediate(() => { throw e });", "        }", "      })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.4.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`uss_reply_msg 存在 `);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`uss_reply_msg`);", "          const value = pm.variables.get(expression)", "      ", "          ", "    const compareValue = await ____replaceIn(\"\");", "    const formattedValues = ____formatValues(value, compareValue, 'exists');", "  ", "          pm.expectWithKey({key:'uss_reply_msg',value: value === null ? '' : formattedValues.value }).to.exist", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}], "responseDefinition": {"id": 567101077, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "2b2f239f-1cf9-40b7-a5a9-a1a4a43329d9", "id": "12d95c26-5083-5e4f-8d32-a0db3c6ff0b0", "type": "http", "name": "USS-查看回复用户弹窗内容，提取变量", "projectId": 4800107, "relatedId": 6669829, "environmentId": 24915297, "blockNumber": 11, "httpApiId": 234868702, "httpApiCaseId": 275592194, "httpApiName": "uss回复用户弹窗", "httpApiPath": "/uss/api/admin/reply/list", "httpApiMethod": "get", "httpApiCaseName": "uss回复用户弹窗"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "678bddc6-303a-5cae-9f82-0ec95091137e", "type": "if", "metaInfo": {"type": "if", "scopeType": "start", "scopeEndId": "1a2d89a6-fe36-5ce0-8eac-20c68146eedf", "events": [{"description": "{{uss_reply_msg}} notEqual undefined", "listen": "test", "script": {"exec": "\n\n  ____string2Array = function(value) {\n    if(typeof value === 'object'){\n      return value;\n    }\n    try {\n      return JSON.parse(value);\n    } catch (e) {\n      return value;\n    }\n  }\n  ____string2Number = function(value, errorMsg) {\n   if(typeof value !== 'string'){\n     return value;\n   }\n   if (/^\\-?\\d+$/.test(value)) {\n       return parseInt(value);\n   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {\n       return parseFloat(value);\n   } else {\n       throw new Error(errorMsg || '数据类型不匹配')\n   }\n }\n\n  ____formatValues = function(value, stringCompareValue, comparison) {\n   try{\n     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);\n     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);\n     let compareValue;\n     switch (typeof value) {\n         case 'string':\n             if (isNumberComparisons) {\n                value = ____string2Number(value);\n                compareValue = ____string2Number(stringCompareValue);\n             } else if (isCollectionComparisons) {\n                compareValue = ____string2Array(stringCompareValue);\n             } else if (comparison === 'exists' || comparison === 'notExist') {\n                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;\n             } else {\n                compareValue = stringCompareValue;\n             }\n             break;\n         case 'object':\n             const isArray = value instanceof Array;\n             if (isNumberComparisons) {\n                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')\n             } else if (isCollectionComparisons && isArray) {\n              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')\n            } else if (\n              isArray &&\n              comparison === 'include' &&\n              value.includes(stringCompareValue)\n            ) {\n              compareValue = stringCompareValue;\n            } else {\n              try {\n                  compareValue = JSON.parse(stringCompareValue);\n              } catch (e) {\n                  compareValue = stringCompareValue;\n              }\n            }\n             break;\n         case 'boolean':\n             if (isNumberComparisons || isCollectionComparisons) {\n                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')\n             }\n             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);\n             break;\n           case 'bigint':\n           case 'number':\n             if (isNumberComparisons) {\n               compareValue = ____string2Number(stringCompareValue);\n             } else if (isCollectionComparisons) {\n              compareValue = ____string2Array(stringCompareValue);\n              value = '' + value;\n            } else {\n               compareValue = stringCompareValue;\n               value = '' + value;\n             }\n             break;\n         case 'null':\n             if (isNumberComparisons) {\n                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')\n             }\n             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;\n             break;\n         default:\n            if (isCollectionComparisons) {\n              compareValue = ____string2Array(stringCompareValue);\n            } else {\n              compareValue = stringCompareValue;\n            }\n            break;\n     }\n     return { compareValue, value };\n   } catch(e) {\n     console.log(e);\n     throw e;\n   }\n }\n \n\n        if (JSON.setEnableBigInt) {\n          JSON.setEnableBigInt(undefined);\n        }\n        \n  async function ____replaceIn(value) {\n    if (typeof pm.variables.replaceInAsync === 'function') {\n      return await pm.variables.replaceInAsync(value);\n    }\n    return pm.variables.replaceIn(value);\n  };\n\n  ;(async () => {\n    try {\n      const formattedName = await ____replaceIn(`undefined 不等于 undefined`);\n      pm.test(formattedName, async function(done) {\n        try {\n          const value = await ____replaceIn(`{{uss_reply_msg}}`);\n          \n    const compareValue = await ____replaceIn(`undefined`);\n    const formattedValues = ____formatValues(value, compareValue, 'notEqual');\n  \n          pm.expect(formattedValues.value).to.not.eql(formattedValues.compareValue);\n          done();\n        } catch (err) {\n          done(err);\n        }\n      });\n    } catch(e) {\n      setImmediate(() => { throw e });\n    }\n  })();\n      ", "id": "if.0.assertion", "type": "text/javascript"}}]}}, {"id": "c4077323-7a8b-53d7-bb24-6f80be26ae76", "type": "delay", "metaInfo": {"id": "c4077323-7a8b-53d7-bb24-6f80be26ae76", "type": "delay", "timeout": 1000}}, {"id": "8901d514-c5ea-5900-b359-c2f32972648e", "name": "USS-查看app接收客服回复是否与后台客服回复消息一致", "request": {"url": {"protocol": "https", "path": ["api", "user", "feedback", "reply", "list"], "host": ["test-h5-oppo-mbi", "allsaints", "top"], "query": [{"disabled": false, "key": "page", "value": "1"}, {"disabled": false, "key": "size", "value": "900"}, {"disabled": false, "key": "uid", "value": "o_id_ZBF63HnuQ93raZ7jnqKAjg"}, {"disabled": false, "key": "utoken", "value": "5578176d2a2b45c5845188202132b116"}, {"disabled": false, "key": "feedbackId", "value": "{{id}}"}], "variable": []}, "header": [{"disabled": false, "key": "Host", "value": "test-h5-oppo-mbi.allsaints.top"}, {"disabled": false, "key": "dev-package", "value": "com.heytap.music"}, {"disabled": false, "key": "dev-region", "value": "cn"}, {"disabled": false, "key": "dev-pid", "value": "fdb5deea9bf44af083e3efc8a4d5ef6c"}, {"disabled": false, "key": "dev-manufacturer", "value": "OPPO"}, {"disabled": false, "key": "dev-language", "value": "zh_cn"}, {"disabled": false, "key": "dev-timezone", "value": "Asia/Shanghai"}, {"disabled": false, "key": "uid", "value": "o_id_ZBF63HnuQ93raZ7jnqKAjg"}, {"disabled": false, "key": "dev-channel", "value": "{{channel}}"}, {"disabled": false, "key": "dev-id", "value": "d41d8cd98f00b204e9800998ecf8427e"}, {"disabled": false, "key": "dev-app-version", "value": "101016060"}, {"disabled": false, "key": "User-Agent", "value": "Mozilla/5.0 (Linux; Android 9; OPPO R11 Plus Build/NMF26X; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/92.0.4515.131 Mobile Safari/537.36 AllSaints_101016060"}, {"disabled": false, "key": "Accept", "value": "application/json, text/plain, */*"}, {"disabled": false, "key": "dev-distinct-id", "value": "6de5db3a-5eee-4c12-903b-595a4e287924"}, {"disabled": false, "key": "dev-model", "value": "OPPO R11 Plus"}, {"disabled": false, "key": "token", "value": "5578176d2a2b45c5845188202132b116"}, {"disabled": false, "key": "dev-brand", "value": "OPPO"}, {"disabled": false, "key": "Origin", "value": "https://test-oppo-h5.allsaints.top"}, {"disabled": false, "key": "X-Requested-With", "value": "com.heytap.music"}, {"disabled": false, "key": "Sec-Fetch-Site", "value": "same-site"}, {"disabled": false, "key": "Sec-Fetch-Mode", "value": "cors"}, {"disabled": false, "key": "Sec-Fetch-Dest", "value": "empty"}, {"disabled": false, "key": "Accept-Language", "value": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7"}], "method": "GET", "baseUrl": "https://test-h5-oppo-mbi.allsaints.top", "body": {}, "auth": {"type": "<PERSON><PERSON><PERSON>", "noauth": []}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`测试场景-在app进行用户反馈，查看是否成功反馈到USS后台并回复`);", "      pm.test(formattedName, async function(done) {", "        try {", "          const value = pm.response.text();", "          ", "    const compareValue = await ____replaceIn(\"\");", "    const formattedValues = ____formatValues(value, compareValue, 'exists');", "  ", "          pm.expectWithKey({key:'undefined',value: value === null ? '' : formattedValues.value }).to.exist", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.code 等于 0000`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.code`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`0000`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.2.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.message 等于 ok`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.message`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`ok`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.3.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`与客服回复消息一致`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data..msg`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`{{uss_reply_msg}}`);", "    const formattedValues = ____formatValues(value, compareValue, 'include');", "  ", "          pm.expect(formattedValues.value).to.include(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}], "responseDefinition": {"id": 564137579, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "8ccebae2-bcbc-4cf9-adba-e364e214e68d", "id": "8901d514-c5ea-5900-b359-c2f32972648e", "type": "http", "name": "USS-查看app接收客服回复是否与后台客服回复消息一致", "projectId": 4800107, "relatedId": 6669829, "environmentId": 24915297, "blockNumber": 14, "httpApiId": 233310315, "httpApiCaseId": 275592197, "httpApiName": "app接收客服回复", "httpApiPath": "/api/user/feedback/reply/list", "httpApiMethod": "get", "httpApiCaseName": "app接收客服回复"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "1a2d89a6-fe36-5ce0-8eac-20c68146eedf", "type": "if", "metaInfo": {"id": "1a2d89a6-fe36-5ce0-8eac-20c68146eedf", "type": "if", "scopeType": "end", "scopeStartId": "678bddc6-303a-5cae-9f82-0ec95091137e"}}, {"id": "ed4a1f5b-2464-56fe-8b01-a15259e3e240", "type": "if", "metaInfo": {"id": "ed4a1f5b-2464-56fe-8b01-a15259e3e240", "type": "if", "scopeType": "end", "scopeStartId": "fad4d1d5-12b5-5c23-ba73-ae72e7edd31a"}}, {"id": "1d55155f-2a76-5ccb-a8dd-1f04ac14fcac", "type": "if", "metaInfo": {"id": "1d55155f-2a76-5ccb-a8dd-1f04ac14fcac", "type": "if", "scopeType": "end", "scopeStartId": "da5174fe-6268-5429-9d40-f4c76eaa7cfb"}}, {"id": "6423a1bd-480c-5031-9488-a989bc7f4ff2", "type": "if", "metaInfo": {"id": "6423a1bd-480c-5031-9488-a989bc7f4ff2", "type": "if", "scopeType": "end", "scopeStartId": "8a95924b-29b6-51b4-bdf8-3779f6ba8917"}}, {"id": "1943c250-ec68-59d7-bb8b-21b36b0ae914", "type": "group", "metaInfo": {"id": "1943c250-ec68-59d7-bb8b-21b36b0ae914", "type": "group", "scopeType": "end", "scopeStartId": "15d28630-fe0e-5690-b59e-a1026d7be351"}}], "name": "uss"}], "info": {"name": "uss"}, "dataSchemas": {}, "mockRules": {"rules": [], "enableSystemRule": true}, "environment": {"id": 24915297, "name": "测试环境", "baseUrl": "https://test-upmp.allsaints.group", "baseUrls": {"default": "https://test-upmp.allsaints.group", "308c62cb-9cc2-418f-bb3b-974a33462e34": "https://test-upmp.allsaints.group", "a47196e6-7551-487d-b543-232e55e09683": "https://test-api-server.allsaints.top", "21211e65-3c46-4c63-b963-7a7a65929263": "https://test-api-oppo-bgm.allsaints.top", "d840ff7e-32d9-4497-8809-f2f124dde270": "https://test-logger-web.allsaints.group", "7bd03f4f-8d2d-47f1-b108-ba5e5bffdbfa": "http://10.194.20.102:8080", "5c1c2eb9-bcf3-4222-95ba-8f635ec27bb3": "http://10.171.3.96:8080", "8a734824-b5a5-4c36-bdf0-233a7d3dfe54": "http://10.171.2.133:8080", "c13fae2a-5dd2-4045-b574-725d20ccf2d6": "http://gw-cms.allsaints-internal.com", "84bc06a8-d32b-48b7-b0e2-9ed78b05147c": "", "a6d5521c-eb4f-416c-ba0d-2cec8a5b9525": "https://test-local-push.allsaints.top", "48734eb2-f8a6-4d94-ba7c-8f480ed34ac7": "https://test-h5-oppo-mbi.allsaints.top", "5266cc13-50f6-459a-b0b4-e9bd4b016e4d": "https://test-welfare-api.allsaintsmusic.com", "88eb862b-ac30-4d66-bf76-a640e6a8f1d0": "https://test-pay-oppo.allsaints.top", "2e740854-ab40-46d2-a3fa-a9b2fcc88b0a": "https://test-music-platform-ui.allsaints.group", "ec7be0b3-1eb9-4f73-aaae-f4aa5212b816": "gw-reco.allsaints-internal.com", "eb35bc81-9704-4e80-9e55-c5f1a2fc66b2": "https://test-h5-oppo-mbi.allsaints.top", "7c318b03-a1c8-4f5c-bfe7-9f4266835e50": "https://test-rms.allsaints.group", "7441c433-7298-4d9c-9e9a-1850c7d97541": "https://test-ringtone.allsaints.top/", "91dcd344-c529-42bc-aabd-7859db7a4d99": "https://test-mms.allsaints.top", "7d8f72ab-e100-445b-aee5-bc950abe3539": "http://211.139.189.130:5818"}, "variable": {"id": "7bcbfdf9-fc51-4e8a-8855-9d5752144f03", "name": "测试环境", "values": [{"type": "any", "value": "test", "key": "env", "isBindInitial": true, "initialValue": "test"}, {"type": "any", "value": "292", "key": "uid", "isBindInitial": true, "initialValue": "292"}, {"type": "any", "value": "CN", "key": "region", "isBindInitial": true, "initialValue": "CN"}, {"type": "any", "value": "1001", "key": "channel", "isBindInitial": true, "initialValue": "1001"}, {"type": "any", "value": "cmsAutoTest", "key": "username", "isBindInitial": true, "initialValue": "cmsAutoTest"}, {"type": "any", "value": "qQxYlm+pacFXjDi8Kxul7+0Ub3cNDnYh6Hx5xDg=", "key": "password", "isBindInitial": true, "initialValue": "qQxYlm+pacFXjDi8Kxul7+0Ub3cNDnYh6Hx5xDg="}, {"type": "any", "value": "C:\\Users\\<USER>\\AppData\\Roaming\\apifox\\ExternalPrograms\\examples/oppo/data/songList_picture.jpg", "key": "fileName", "isBindInitial": false, "initialValue": ""}, {"type": "any", "value": "uY-d0sqVOEpUdMesuI2UswAV7TczI9gt0sZuXHxosUWnzK18MBdcZeqIx98l8aRaQvA1Oo3VW9VWZTNq0Vm6I-UU2GxzzjEwFRVkfMi9k3LRbTaBX36DofzRSdv2EckY", "key": "access_token", "isBindInitial": false, "initialValue": ""}, {"type": "any", "value": "4PmiZe_2I-RtKGXXXdoeNMmhZ2pA4TYeZYFnxb_09Nkzd3VSQSNIylFanXw_TSleqFvK9ZE_McFGA2jXypFvz6aoMnrQY4iJFrbaAfKtPRoSAq2uvbHzoJdogN4e-p9c", "key": "refresh_token", "isBindInitial": false, "initialValue": ""}, {"type": "any", "value": "0", "key": "vip_time", "isBindInitial": false, "initialValue": ""}, {"type": "any", "value": "", "key": "bgm_headers", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "bgm_appid", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "ultimate_headers", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "qingting_headers", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "ximalaya_body", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "HomePageId", "isBindInitial": true, "initialValue": "43988"}, {"type": "any", "value": "", "key": "YueKuId", "isBindInitial": true, "initialValue": "1306"}, {"type": "any", "value": "", "key": "TingshuHomePageId", "isBindInitial": true, "initialValue": "24254"}, {"type": "any", "value": "", "key": "HomePageJgqId", "isBindInitial": true, "initialValue": "43989"}, {"type": "any", "value": "", "key": "YinYueZhuanQuId", "isBindInitial": true, "initialValue": "15022"}, {"type": "any", "value": "", "key": "Host", "isBindInitial": true, "initialValue": "test-api-server.allsaints.top"}, {"type": "any", "value": "", "key": "variable_key", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "Apple", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "labelname", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "allLabels", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "tabLabel", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "jgq_id", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "*************", "key": "mongoDB_ip", "isBindInitial": false, "initialValue": ""}, {"type": "any", "value": "rwuser", "key": "mongoDB_username", "isBindInitial": false, "initialValue": "rwuser"}, {"type": "any", "value": "^BtCjnjQMq2T", "key": "mongoDB_password", "isBindInitial": false, "initialValue": "^BtCjnjQMq2T"}, {"type": "any", "value": "pms_meta", "key": "mongoDB_pms", "isBindInitial": false, "initialValue": "pms_meta"}, {"type": "any", "value": "admin", "key": "mongoDB_admin", "isBindInitial": false, "initialValue": "admin"}]}, "requestProxyAgentSettings": {"default": {"agentId": 2}, "308c62cb-9cc2-418f-bb3b-974a33462e34": {"agentId": 2}, "a47196e6-7551-487d-b543-232e55e09683": {"agentId": 2}, "21211e65-3c46-4c63-b963-7a7a65929263": {"agentId": 2}, "d840ff7e-32d9-4497-8809-f2f124dde270": {"agentId": 2}, "7bd03f4f-8d2d-47f1-b108-ba5e5bffdbfa": {"agentId": 2}, "5c1c2eb9-bcf3-4222-95ba-8f635ec27bb3": {"agentId": 2}, "8a734824-b5a5-4c36-bdf0-233a7d3dfe54": {"agentId": 2}, "c13fae2a-5dd2-4045-b574-725d20ccf2d6": {"agentId": 2}, "84bc06a8-d32b-48b7-b0e2-9ed78b05147c": {"agentId": 2}, "a6d5521c-eb4f-416c-ba0d-2cec8a5b9525": {"agentId": 2}, "5266cc13-50f6-459a-b0b4-e9bd4b016e4d": {"agentId": 2}, "48734eb2-f8a6-4d94-ba7c-8f480ed34ac7": {"agentId": 2}, "88eb862b-ac30-4d66-bf76-a640e6a8f1d0": {"agentId": 2}, "2e740854-ab40-46d2-a3fa-a9b2fcc88b0a": {"agentId": 2}, "ec7be0b3-1eb9-4f73-aaae-f4aa5212b816": {"agentId": 2}, "eb35bc81-9704-4e80-9e55-c5f1a2fc66b2": {"agentId": 2}, "7c318b03-a1c8-4f5c-bfe7-9f4266835e50": {"agentId": 2}, "7441c433-7298-4d9c-9e9a-1850c7d97541": {"agentId": 2}, "91dcd344-c529-42bc-aabd-7859db7a4d99": {"agentId": 2}, "343c36aa-**************-6b81d2fbd9b9": {"agentId": 2}, "7d8f72ab-e100-445b-aee5-bc950abe3539": {"agentId": 2}}, "type": "normal", "parameter": {"header": [], "query": [], "body": [], "cookie": []}}, "globals": {"baseUrl": "", "baseUrls": {}, "variable": {"id": "a9bd4902-0b69-476f-b380-e1473fa01de1", "values": [{"type": "any", "value": "", "key": "access_token", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "uid", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "region", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "channel", "isBindInitial": true, "initialValue": ""}]}, "parameter": {"header": [], "query": [], "body": [], "cookie": []}}, "isServerBuild": false, "isTestFlowControl": true, "projectOptions": {"enableJsonc": false, "enableBigint": false, "responseValidate": true, "isDefaultUrlEncoding": 2, "enableTestScenarioSetting": false, "enableYAPICompatScript": false, "publishedDocUrlRules": {"defaultRule": "RESOURCE_KEY_ONLY", "resourceKeyStandard": "LEGACY"}, "folderShareExpandModeSettings": {"expandId": [], "mode": "AUTO"}, "mockSettings": {"engine": "fakerjs"}, "language": "zh-CN"}}
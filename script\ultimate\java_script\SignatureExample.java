package com.example;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.*;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

// 爱听刷新CDN密匙生成脚本
public class SignatureExample {
    static class FreshCdnForm {
        private Integer type;

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public List<SongData> getData() {
            return data;
        }

        public void setData(List<SongData> data) {
            this.data = data;
        }

        private List<SongData> data;

        public static class SongData {
            public Long getSongId() {
                return songId;
            }

            public void setSongId(Long songId) {
                this.songId = songId;
            }

            public String getUrl() {
                return url;
            }

            public void setUrl(String url) {
                this.url = url;
            }

            private Long songId;
            private String url;
        }
    }

    private static final String PRIVATE_KEY = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALQ76T5iH1G/u+emkgmvCw+lZDowlO+fod+Il2LIVapAszqEzL/Xm1tOGRgugW03M78WvHYCHDMfJLm2srxcozvQGx18BZQjCnvO0nP1SQqy5VqyrEbAj+tqM/uLcuFag+uXbuiq/1GTe99OvZmkUekqtlrOxBzkWSGLKEfRWVpRAgMBAAECgYBa429+xwqWNgMzEVvJyFzimfwOIBurLyv5Rq9Y4D/a6F+5neiGwdqVU6/x8tnP0qukrHmDZRMFcKobgRdnR6wEkAt1XEiR3wuCGOmNXA0elXUQRMnsNKHyoouE2RN3trqFElrkkwBdDNVihmouoG5k679i0WeSQRl/fAmqOMdUcQJBANcvrdlZRYCGC5Nyg7a3jarT5lEy1AY0W/QkaRcYKoCgWWVorNGpRfEs/p1KS+HhoTxR7J95njVDCrxcvE3FQBUCQQDWayJ8jiCUZK+nTbO8MrvHKqOkBPptWFIJ6qyrnMY/sZxGJh/6utw2BbqPkEgPuJSB/8YG3nSL/5z7K9OgYcRNAkAaoO3UjbWKGAxqrKsb+07uqtY/ihiuw9/1MGRI1Va9IBqv7+oi792V4MmJUV5ej3tSaZjsizJGyQsVECzFOvmZAkEAuglObL9sKRSLCx/x2FI0doPaY48mMQU2eQAyPNvTbqQNsReXG5ZRRlYGHlXTEEDYKUrgaElO2cE4VP0bqsXo6QJAJUNKdmFmxUMUrcBCChFh4PU6Pl0moh37fQHuy8L8wDaWRFLFQh9wglhojTrsZQUTH5s9exV+R27KlzPUPgn5zw==";
    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static void main(String[] args) throws Exception {
        // 模拟FreshCdnForm数据
        FreshCdnForm freshCdnForm = new FreshCdnForm();
        freshCdnForm.setType(1);
        final FreshCdnForm.SongData songData = new FreshCdnForm.SongData();
        songData.setSongId(53044L);
        songData.setUrl("https://test-static5.allsaints.top/2024/05/14/a/1790200058403041280_800*800.webp");
        final List<FreshCdnForm.SongData> list = List.of(songData);
        freshCdnForm.setData(list);
        final HashMap<String, Object> map = new HashMap<>();
        map.put("type", 1L);
        map.put("data", list);
        // 创建待签名的明文列表
        List<String> plainTxtList = new ArrayList<>();
        final String nonce = "2BIAiswCYv";
        plainTxtList.add(nonce);
        // final String timestamp = "1722234134329";
        String timestamp = String.valueOf(System.currentTimeMillis());
        plainTxtList.add(timestamp);
        plainTxtList.add(String.valueOf(freshCdnForm.getType()));
        plainTxtList.add(objectMapper.writeValueAsString(freshCdnForm.getData()));

        // 排序
        Collections.sort(plainTxtList);

        // 生成签名
        String signature = sign(plainTxtList.toString(), PRIVATE_KEY);

        // System.out.println("nonce: " + nonce);
        // System.out.println("timestamp: " + timestamp);
        // System.out.println("Signature: " + signature);
        // System.out.println("data:" + objectMapper.writeValueAsString(map));

        // 创建一个包含 nonce、timestamp 和 signature 的 JsonObject 给apifox公共脚本调用，需要输出一个json字符串
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("nonce", nonce);
        jsonObject.addProperty("timestamp", timestamp);
        jsonObject.addProperty("signature", signature);

        // 使用 Gson 将 JsonObject 转换为 JSON 字符串
        Gson gson = new Gson();
        String jsonString = gson.toJson(jsonObject);

        // 输出 JSON 字符串
        System.out.println(jsonString);

    }

    private static String sign(String data, String privateKeyStr) throws Exception {
        byte[] privateKeyBytes = Base64.getDecoder().decode(privateKeyStr);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(keySpec);

        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(privateKey);
        signature.update(data.getBytes());

        byte[] signedBytes = signature.sign();
        return Base64.getEncoder().encodeToString(signedBytes);
    }
}
# This is a sample Python script.

# Press Shift+F10 to execute it or replace it with your code.
# Press Double Shift to search everywhere for classes, files, tool windows, actions, and settings.
# import redis
from rediscluster import RedisCluster


from rediscluster import RedisCluster
import sys

uid = sys.argv[1]  # 第一个参数
def welfare_add_coin(uid):
    # Redis 集群节点的地址，可以填写多个节点进行容错
    startup_nodes = [
        {"host": "redis-6639c159-ca66-4f8d-b346-cf9686bea635.cn-east-3.dcs.myhuaweicloud.com", "port": "6379"}
    ]

    # 用户 ID 和对应的 Redis 键
    user_key = f"ASW_USER_COIN:1001:CN:{uid}"
    threshold_value = 20000

    try:
        # 创建 Redis 集群连接
        rc = RedisCluster(startup_nodes=startup_nodes, decode_responses=True)

        # 获取当前用户的值
        current_value = rc.get(user_key)

        if current_value:
            # 将当前值转换为整数
            current_value = int(current_value)

            # 判断当前值是否大于等于 20000
            if current_value >= threshold_value:
                print(f"用户的当前值 {current_value} 已经大于或等于 {threshold_value}, 不做任何操作.")
            else:
                # 如果小于 200000，则更新值为 20000
                rc.set(user_key, threshold_value)
                print(f"用户的当前值 {current_value} 小于 {threshold_value}, 已经更新为 {threshold_value}.")
        else:
            # 如果值不存在，则设置为 20000
            rc.set(user_key, threshold_value)
            print(f"用户的当前值不存在, 已经设置为 {threshold_value}.")

    except Exception as e:
        print("连接失败:", str(e))







if __name__ == '__main__':
    res = welfare_add_coin(uid)

# -*- coding:utf-8 -*-
# @Time: 2024/9/26 14:49
# @Author: wenjie.hu
# @Email: <EMAIL>

import sys
import ast
import os
import requests
import time
import datetime
import json

# 动态添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)  # 获取项目根目录
if project_root not in sys.path:
    sys.path.insert(0, project_root)
from tool.parse_report import parse_apifox_report, generate_test_scenario_excel


def send_feishu():
    # 默认发送错误消息
    title_desc = f"[{CONF['APIFOX_CASE_NAME']}] 测试失败"
    title_font = "red"  # 标题颜色
    # 获取代码提交时间
    if CONF.get('GIT_COMMIT_TIME'):
        time1 = int(int(CONF['GIT_COMMIT_TIME']) * 1000)
        time2 = datetime.datetime.fromtimestamp(time1 / 1000)
        CONF['GIT_COMMIT_TIME'] = time2.strftime("%Y-%m-%d %H:%M:%S")  # 时间戳改为时分秒格式,给报告使用
    else:
        CONF['GIT_COMMIT_TIME'] = ""
    if CONF['RESULT'] == "PASS":
        title_desc = f"[{CONF['APIFOX_CASE_NAME']}] 测试通过"
        title_font = "green"  # 标题颜色
        # 打包报告模板
    payload = {}
    if apifox_result:
        # 显示基本信息
        basic_info = apifox_result.get('basic_info', {})
        # 显示统计信息
        statistics = apifox_result.get('statistics', {})
        # 用例PASS总数
        http_requests_pass = statistics.get('http_requests_total', 0) - statistics.get('http_requests_failed', 0)
        # 断言PASS总数
        assertions_pass = statistics.get('assertions_total', 0) - statistics.get('assertions_failed', 0)
        total_duration = str(basic_info.get('total_duration', 0))
        total_duration = total_duration.replace("ms","毫秒")
        total_duration = total_duration.replace("d", "天")
        total_duration = total_duration.replace("h", "时")
        total_duration = total_duration.replace("m","分")
        total_duration = total_duration.replace("s", "秒")
        case_pass_rate =  test_data.get('cases_pass_count', 0) / test_data.get('cases_count', 0)
        case_pass_rate = f"{round(case_pass_rate*100,2)}%"
        payload = json.dumps({
            "msg_type": "interactive",
            "card": {
                "type": "template",
                "data": {
                    "template_id": "AAqdXsSk1UkD8",
                    "template_version_name": "1.0.3",
                    "template_variable": {
                        "test_scenario": str(basic_info.get('test_scenario', 'N/A')),
                        "run_time": str(basic_info.get('run_time', 'N/A')),
                        "JOB_NAME": CONF['JOB_NAME'],
                        "BUILD_NUMBER": CONF['BUILD_NUMBER'],
                        "GIT_BRANCH": CONF['GIT_BRANCH'],
                        "GIT_TAG": CONF['GIT_TAG'],
                        "GIT_COMMIT": CONF['GIT_COMMIT'],
                        "GIT_LOG": CONF['GIT_LOG'],
                        "GIT_COMMIT_TIME": CONF['GIT_COMMIT_TIME'],
                        "run_tool": basic_info.get('run_tool', 'N/A'),
                        "api_request_duration": str(basic_info.get('api_request_duration', 'N/A')),
                        "avg_api_request_duration": str(basic_info.get('avg_api_request_duration', 'N/A')),
                        "http_requests_total": str(statistics.get('http_requests_total', 'N/A')),
                        "total_duration": total_duration,
                        "pass_rate": case_pass_rate,
                        "DOW_URL": {
                            "pc_url": DOW_URL,
                            "android_url": DOW_URL,
                            "ios_url": DOW_URL,
                            "url": DOW_URL
                        },
                        "LOG_URL": {
                            "pc_url": LOG_URL,
                            "android_url": LOG_URL,
                            "ios_url": LOG_URL,
                            "url": LOG_URL
                        },
                        "table_raw_array": [
                            {
                                "name": "用例总数",
                                "fail_num": str(test_data.get('cases_fail_count', 'N/A')),
                                "pass_num": str(test_data.get('cases_pass_count', 'N/A')),
                                "total_num": str(test_data.get('cases_count', 'N/A'))
                            },
                            {
                                "name": "接口总数",
                                "fail_num": str(statistics.get('http_requests_failed', 'N/A')),
                                "pass_num": str(http_requests_pass),
                                "total_num": str(statistics.get('http_requests_total', 'N/A'))
                            },
                            {
                                "name": "断言总数",
                                "fail_num": str(statistics.get('assertions_failed', 'N/A')),
                                "pass_num": str(assertions_pass),
                                "total_num": str(statistics.get('assertions_total', 'N/A'))
                            }
                        ],
                        "RESULT": CONF['RESULT'],
                        "title_desc": title_desc,
                        "RESULT_COLOR": title_font,
                        'cases_count': str(test_data.get('cases_count', 'N/A'))
                    }
                }
            }
        })
    # 发送飞书测试群消息
    headers = {
        'Content-Type': 'application/json'
    }
    requests.request(method="post", url=url_test, headers=headers, data=payload)


def send_err_msg():
    # 默认发送错误消息
    title_desc = f"[{CONF['APIFOX_CASE_NAME']}] 测试异常"
    title_font = "red"  # 标题颜色
    # 获取代码提交时间
    if CONF.get('GIT_COMMIT_TIME'):
        time1 = int(int(CONF['GIT_COMMIT_TIME']) * 1000)
        time2 = datetime.datetime.fromtimestamp(time1 / 1000)
        CONF['GIT_COMMIT_TIME'] = time2.strftime("%Y-%m-%d %H:%M:%S")  # 时间戳改为时分秒格式,给报告使用
    else:
        CONF['GIT_COMMIT_TIME'] = ""
    payload = json.dumps({
        "msg_type": "interactive",
        "card": {
            "type": "template",
            "data": {
                "template_id": "AAqd5Hyphc57v",
                "template_version_name": "1.0.1",
                "template_variable": {
                    "JOB_NAME": CONF['JOB_NAME'],
                    "BUILD_NUMBER": CONF['BUILD_NUMBER'],
                    "GIT_BRANCH": CONF['GIT_BRANCH'],
                    "GIT_TAG": CONF['GIT_TAG'],
                    "GIT_COMMIT": CONF['GIT_COMMIT'],
                    "GIT_LOG": CONF['GIT_LOG'],
                    "GIT_COMMIT_TIME": CONF['GIT_COMMIT_TIME'],
                    "LOG_URL": {
                        "pc_url": LOG_URL,
                        "android_url": LOG_URL,
                        "ios_url": LOG_URL,
                        "url": LOG_URL
                    },
                    "RESULT": "提示：执行测试脚本发生异常，请检查执行日志！",
                    "title_desc": title_desc,
                    "RESULT_COLOR": title_font,
                }
            }
        }
    })
    # 发送飞书测试群消息
    headers = {
        'Content-Type': 'application/json'
    }
    requests.request(method="post", url=url_test, headers=headers, data=payload)

if __name__ == '__main__':

    CONF = ast.literal_eval(sys.argv[1])
    # CONF = {"JOB_URL": "http://10.194.9.116:8080/job/autoTest/job/auto_test_oppo/",
    #         "JOB_NAME": "autoTest/auto_test_oppo", "JOB_BASE_NAME": "auto_test_oppo", "BUILD_NUMBER": "40",
    #         "BUILD_URL": "http://10.194.9.116:8080/job/autoTest/job/auto_test_oppo/23/", "GIT_BRANCH": "master",
    #         "GIT_TAG": "b512781", "GIT_LOG": "增加了循环调用后的等待时间", "GIT_COMMIT_TIME": "1749090259",
    #         "GIT_COMMIT": "b51278174c409b3831125df9561dcb26fd6554f2", "APIFOX_CASE_NAME": "oppo_OPPO测试环境主流程验证",
    #         "RESULT": "FAIL", "RESULT_DESC": "测试不通过，详情可查看执行日志/测试报告！"}
    # print('jenkins传入配置:%s' % CONF)
    url_test = 'https://open.feishu.cn/open-apis/bot/v2/hook/31e1a930-130c-4987-a7db-7465c2c443fc'  # 自动群机器人API
    DOW_URL = f"http://test-hw-jenkins.allsaints.group:8080/job/autoTest/job/{CONF['JOB_BASE_NAME']}/{CONF['BUILD_NUMBER']}/artifact/apifox-reports/*zip*/apifox-reports.zip"
    LOG_URL = f"http://test-hw-jenkins.allsaints.group:8080/job/autoTest/job/{CONF['JOB_BASE_NAME']}/{CONF['BUILD_NUMBER']}/console"

    try:
        # 解析apifox的html报告数据
        # 服务器路径
        apifox_result = parse_apifox_report(f"./apifox-reports/{CONF['PROJECT_NAME']}_{CONF['BACKEND_NAME']}_report_{CONF['BUILD_NUMBER']}.html")
        test_data = generate_test_scenario_excel(apifox_result,
                                                 f"./apifox-reports/{CONF['PROJECT_NAME']}_{CONF['BACKEND_NAME']}_case_{CONF['BUILD_NUMBER']}.xlsx")
        # 本地路径
        # apifox_result = parse_apifox_report(f"../apifox-reports/{CONF['JOB_BASE_NAME']}_{CONF['BUILD_NUMBER']}.html")
        # test_data = generate_test_scenario_excel(apifox_result,
        #                                          f"../apifox-reports/{CONF['JOB_BASE_NAME']}_{CONF['BUILD_NUMBER']}.xlsx")
        send_feishu()
    except Exception as e:
        send_err_msg()

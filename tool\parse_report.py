# -*- coding:utf-8 -*-
# @Time: 2025/6/18 14:49
# @Author: wenjie.hu
# @Email: <EMAIL>

import re
import json
from bs4 import BeautifulSoup
from typing import Dict, List, Any, Optional
from collections import defaultdict

try:
    import pandas as pd
    import openpyxl
    from openpyxl.styles import Font, PatternFill, Alignment

    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False
    print("警告: pandas 或 openpyxl 未安装，Excel功能将不可用")
    print("请运行: pip install pandas openpyxl")


def parse_apifox_report(html_file_path: str) -> Dict[str, Any]:
    """
    解析Apifox测试报告HTML文件，提取测试数据并返回Python字典

    Args:
        html_file_path (str): HTML文件路径

    Returns:
        Dict[str, Any]: 包含测试报告数据的字典
    """
    try:
        # 读取HTML文件
        with open(html_file_path, 'r', encoding='utf-8') as file:
            html_content = file.read()

        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(html_content, 'html.parser')

        # 初始化结果字典
        report_data = {
            'basic_info': {},
            'statistics': {},
            'test_cases': []
        }

        # 解析所有信息（合并到一个函数中）
        _parse_all_info(soup, report_data)

        # 解析测试用例
        report_data['test_cases'] = _parse_test_cases(soup)

        return report_data

    except Exception as e:
        print(f"解析报告文件时出错: {str(e)}")
        return {}


def _parse_all_info(soup: BeautifulSoup, report_data: Dict[str, Any]) -> None:
    """解析所有基本信息、统计信息和性能信息"""
    try:
        # 查找第一个card-body，包含所有基本信息
        card_body = soup.find('div', class_='card-body')
        if not card_body:
            return

        # 查找所有的div元素
        all_divs = card_body.find_all('div')

        # 遍历所有div，寻找标签-值对
        for i, div in enumerate(all_divs):
            text = div.get_text(strip=True)

            # 检查是否是我们需要的标签
            if text in ['测试场景', '运行时间', '运行工具']:
                # 查找下一个包含值的div
                next_div = _find_next_value_div(all_divs, i)
                if next_div:
                    value = next_div.get_text(strip=True)
                    key = _get_english_key(text)
                    report_data['basic_info'][key] = value

            elif text in ['总耗时', '总返回数据', '接口请求耗时', '平均接口请求耗时', '通过率', '失败率', '未测率']:
                next_div = _find_next_value_div(all_divs, i)
                if next_div:
                    value = next_div.get_text(strip=True)
                    key = _get_english_key(text)
                    report_data['basic_info'][key] = value

            elif text in ['循环数', 'HTTP 接口请求数', '断言数']:
                # 这些有总数和失败数两个值
                total_div = _find_next_value_div(all_divs, i)
                failed_div = _find_next_value_div(all_divs, i + 1) if total_div else None

                if total_div:
                    total_value = _extract_number(total_div.get_text(strip=True))
                    failed_value = _extract_number(failed_div.get_text(strip=True)) if failed_div else 0

                    base_key = _get_english_key(text)
                    report_data['statistics'][f'{base_key}_total'] = total_value
                    report_data['statistics'][f'{base_key}_failed'] = failed_value

    except Exception as e:
        print(f"解析信息时出错: {str(e)}")


def _find_next_value_div(all_divs: List, current_index: int):
    """查找下一个包含值的div"""
    for i in range(current_index + 1, len(all_divs)):
        div = all_divs[i]
        text = div.get_text(strip=True)
        # 跳过空白和已知的标签
        if text and text not in ['总数', '失败数', '&nbsp;', '']:
            # 检查是否不是另一个标签
            if text not in ['测试场景', '运行时间', '运行工具', '循环数', 'HTTP 接口请求数', '断言数',
                            '总耗时', '总返回数据', '接口请求耗时', '平均接口请求耗时', '通过率', '失败率', '未测率']:
                return div
    return None


def _get_english_key(chinese_label: str) -> str:
    """将中文标签转换为英文键"""
    mapping = {
        '测试场景': 'test_scenario',
        '运行时间': 'run_time',
        '运行工具': 'run_tool',
        '循环数': 'loops',
        'HTTP 接口请求数': 'http_requests',
        '断言数': 'assertions',
        '总耗时': 'total_duration',
        '总返回数据': 'total_response_size',
        '接口请求耗时': 'api_request_duration',
        '平均接口请求耗时': 'avg_api_request_duration',
        '通过率': 'pass_rate',
        '失败率': 'fail_rate',
        '未测率': 'untested_rate'
    }
    return mapping.get(chinese_label, chinese_label.lower().replace(' ', '_'))


def _parse_test_cases(soup: BeautifulSoup) -> List[Dict[str, Any]]:
    """解析测试用例"""
    test_cases = []

    try:
        # 查找所有测试用例卡片
        test_cards = soup.find_all('div', class_='card', id=lambda x: x and x.startswith('request-'))

        for card in test_cards:
            test_case = {}

            # 提取测试用例名称
            header = card.find('h5', class_='card-header')
            if header:
                test_case['name'] = header.get_text(strip=True)

            # 提取测试用例详细信息
            card_body = card.find('div', class_='card-body')
            if card_body:
                test_case.update(_parse_test_case_details(card_body))

            test_cases.append(test_case)

    except Exception as e:
        print(f"解析测试用例时出错: {str(e)}")

    return test_cases


def _parse_test_case_details(card_body) -> Dict[str, Any]:
    """解析单个测试用例的详细信息"""
    details = {}

    try:
        # 查找所有行
        rows = card_body.find_all('div', class_='row')
        if rows:
            row = rows[0]  # 主要信息在第一个row中
            cols = row.find_all('div', class_=['col-md-4', 'col-md-8'])

            # 提取基本信息
            for i in range(0, len(cols), 2):
                if i + 1 < len(cols):
                    label = cols[i].get_text(strip=True)
                    value = cols[i + 1].get_text(strip=True)

                    # 映射测试用例字段
                    if label == 'Method':
                        details['method'] = value
                    elif label == 'URL':
                        # 提取URL链接
                        link = cols[i + 1].find('a')
                        if link:
                            details['url'] = link.get('href', value)
                        else:
                            details['url'] = value
                    elif '耗时' in label:
                        details['duration'] = value
                    elif '返回数据' in label:
                        details['response_size'] = value
                    elif 'HTTP 状态码' in label:
                        details['status_code'] = _extract_number(value)
                    elif '断言通过数' in label:
                        details['assertions_passed'] = _extract_number(value)
                    elif '断言失败数' in label:
                        details['assertions_failed'] = _extract_number(value)

        # 解析断言详情
        details['assertion_details'] = _parse_assertion_details(card_body)

        # 解析请求和响应数据
        details['request_data'] = _parse_request_response_data(card_body, 'request')
        details['response_data'] = _parse_request_response_data(card_body, 'response')

    except Exception as e:
        print(f"解析测试用例详情时出错: {str(e)}")

    return details


def _parse_assertion_details(card_body) -> List[Dict[str, str]]:
    """解析断言详情"""
    assertions = []

    try:
        # 查找断言表格
        table = card_body.find('table', class_='table')
        if table:
            tbody = table.find('tbody')
            if tbody:
                rows = tbody.find_all('tr')
                for row in rows:
                    cols = row.find_all('td')
                    if len(cols) >= 3:
                        assertion = {
                            'name': cols[0].get_text(strip=True),
                            'result': cols[1].get_text(strip=True),
                            'details': cols[2].get_text(strip=True)
                        }
                        assertions.append(assertion)

    except Exception as e:
        print(f"解析断言详情时出错: {str(e)}")

    return assertions


def _parse_request_response_data(card_body, data_type: str) -> Optional[Dict[str, Any]]:
    """解析请求或响应数据"""
    try:
        # 查找对应的代码块
        if data_type == 'request':
            # 查找实际请求数据
            collapse_div = card_body.find('div', class_='collapse')
            if collapse_div:
                code_block = collapse_div.find('code')
                if code_block:
                    json_text = code_block.get_text(strip=True)
                    return _parse_json_safely(json_text)
        elif data_type == 'response':
            # 查找返回响应数据
            response_sections = card_body.find_all('div', class_='col-md-10')
            for section in response_sections:
                prev_sibling = section.find_previous_sibling('div', class_='col-md-2')
                if prev_sibling and '返回响应' in prev_sibling.get_text():
                    code_block = section.find('code')
                    if code_block:
                        json_text = code_block.get_text(strip=True)
                        return _parse_json_safely(json_text)

    except Exception as e:
        print(f"解析{data_type}数据时出错: {str(e)}")

    return None


def _extract_number(text: str) -> int:
    """从文本中提取数字"""
    try:
        # 使用正则表达式提取数字
        numbers = re.findall(r'\d+', text)
        if numbers:
            return int(numbers[0])
    except:
        pass
    return 0


def _parse_json_safely(json_text: str) -> Optional[Dict[str, Any]]:
    """安全地解析JSON文本"""
    try:
        # 清理JSON文本
        json_text = json_text.strip()
        if json_text.startswith('{') and json_text.endswith('}'):
            return json.loads(json_text)
    except json.JSONDecodeError:
        pass
    return None


def print_report_summary(report_data: Dict[str, Any]) -> None:
    """打印报告摘要信息"""
    print("=" * 60)
    print("Apifox 测试报告解析结果")
    print("=" * 60)

    # 基本信息
    if 'basic_info' in report_data:
        print("\n【基本信息】")
        for key, value in report_data['basic_info'].items():
            print(f"  {key}: {value}")

    # 统计信息
    if 'statistics' in report_data:
        print("\n【统计信息】")
        for key, value in report_data['statistics'].items():
            print(f"  {key}: {value}")

    # 性能信息
    if 'performance' in report_data:
        print("\n【性能信息】")
        for key, value in report_data['performance'].items():
            print(f"  {key}: {value}")

    # 测试用例概览
    if 'test_cases' in report_data:
        print(f"\n【测试用例】共 {len(report_data['test_cases'])} 个")
        for i, test_case in enumerate(report_data['test_cases'][:5], 1):  # 只显示前5个
            name = test_case.get('name', f'测试用例{i}')
            method = test_case.get('method', 'N/A')
            status = test_case.get('status_code', 'N/A')
            print(f"  {i}. {name} [{method}] - 状态码: {status}")

        if len(report_data['test_cases']) > 5:
            print(f"  ... 还有 {len(report_data['test_cases']) - 5} 个测试用例")


def main():
    """主函数 - 示例用法"""
    # 示例：解析报告文件
    html_file_path = "../apifox-reports/zte_努比亚测试环境主流程验证_report_2.html"

    print("开始解析 Apifox 测试报告...")
    report_data = parse_apifox_report(html_file_path)

    if report_data:
        print("解析成功！")
        print_report_summary(report_data)

        # 可以将结果保存为JSON文件
        output_file = "../apifox-reports/parsed_report.json"
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            print(f"\n解析结果已保存到: {output_file}")
        except Exception as e:
            print(f"保存文件时出错: {str(e)}")

        # 生成测试场景执行结果Excel文档
        excel_output_file = "../apifox-reports/test_scenario_results.xlsx"
        print(f"\n开始生成测试场景执行结果Excel文档...")
        generate_test_scenario_excel(report_data, excel_output_file)
    else:
        print("解析失败！")


def simple_parse_example(html_file_path: str) -> Dict[str, Any]:
    """
    简单的解析示例函数

    Args:
        html_file_path (str): HTML文件路径

    Returns:
        Dict[str, Any]: 解析后的数据字典

    Example:
        # 使用示例
        result = simple_parse_example("apifox-reports/auto_test_oppo_23.html")
        print(f"测试场景: {result['basic_info']['test_scenario']}")
        print(f"总请求数: {result['statistics']['http_requests_total']}")
        print(f"失败请求数: {result['statistics']['http_requests_failed']}")
    """
    return parse_apifox_report(html_file_path)


def generate_excel_from_html(html_file_path: str, excel_output_path: str = None) -> None:
    """
    从HTML报告文件直接生成测试场景执行结果Excel文档

    Args:
        html_file_path (str): HTML报告文件路径
        excel_output_path (str): Excel输出文件路径，默认为None（自动生成）

    Example:
        # 使用示例
        generate_excel_from_html("apifox-reports/auto_test_oppo_23.html")
        # 或指定输出路径
        generate_excel_from_html("apifox-reports/auto_test_oppo_23.html", "my_test_results.xlsx")
    """
    print(f"开始解析HTML报告文件: {html_file_path}")

    # 解析HTML报告
    report_data = parse_apifox_report(html_file_path)

    if not report_data:
        print("解析HTML报告失败！")
        return

    # 如果没有指定输出路径，则自动生成
    if excel_output_path is None:
        import os
        base_name = os.path.splitext(os.path.basename(html_file_path))[0]
        excel_output_path = f"{base_name}_test_scenario_results.xlsx"

    print(f"开始生成测试场景执行结果Excel文档: {excel_output_path}")

    # 生成Excel文档
    generate_test_scenario_excel(report_data, excel_output_path)

    print("处理完成！")


def generate_test_scenario_excel(report_data: Dict[str, Any], output_file: str = "test_scenario_results.xlsx") -> dict[
                                                                                                                      str, int] | None:
    """
    根据解析的报告数据生成测试场景执行结果Excel文档

    Args:
        report_data (Dict[str, Any]): 解析后的报告数据
        output_file (str): 输出Excel文件路径，默认为"test_scenario_results.xlsx"
    """
    if not EXCEL_AVAILABLE:
        print("错误: pandas 或 openpyxl 未安装，无法生成Excel文件")
        print("请运行: pip install pandas openpyxl")
        return

    try:
        # 获取test_cases数据
        test_cases = report_data.get('test_cases', [])
        if not test_cases:
            print("未找到测试用例数据")
            return

        # 用于存储测试场景数据的列表，保持原始顺序
        excel_data = []
        scenario_stats = defaultdict(int)  # 用于统计
        
        # 用于保存每个测试场景的第一个用例的测试步骤，用于提取模块信息
        scenario_first_steps = {}

        # 先遍历一遍找出每个测试场景的第一个测试用例，用于后面提取模块信息
        for test_case in test_cases:
            test_case_name = test_case.get('name', '')
            assertion_details = test_case.get('assertion_details', [])

            # 过滤掉assertion_details为空的情况
            if not assertion_details:
                continue

            # 找到该测试用例中包含"测试场景-"的断言，作为测试场景名称
            scenario_name = None
            for assertion in assertion_details:
                assertion_name = assertion.get('name', '')
                scenario_match = re.search(r'测试场景-(.+)', assertion_name)
                if scenario_match:
                    scenario_name = scenario_match.group(1)  # 只保留"测试场景-"后面的内容
                    break

            # 如果找到了测试场景，且这个场景还没有记录第一个测试用例，则记录下来
            if scenario_name and scenario_name not in scenario_first_steps:
                scenario_first_steps[scenario_name] = test_case_name

        # 按照test_cases的原始顺序遍历所有测试用例
        for test_case in test_cases:
            test_case_name = test_case.get('name', '')
            assertion_details = test_case.get('assertion_details', [])

            # 过滤掉assertion_details为空的情况
            if not assertion_details:
                continue

            # 找到该测试用例中包含"测试场景-"的断言，作为测试场景名称
            scenario_name = None
            for assertion in assertion_details:
                assertion_name = assertion.get('name', '')
                scenario_match = re.search(r'测试场景-(.+)', assertion_name)
                if scenario_match:
                    scenario_name = scenario_match.group(1)  # 只保留"测试场景-"后面的内容
                    break

            # 如果找到了测试场景，则处理该测试用例的所有断言
            if scenario_name:
                # 提取一级模块和二级模块（使用该测试场景的第一个用例的测试步骤）
                first_step = scenario_first_steps.get(scenario_name, '')
                primary_module = '无'
                secondary_module = '无'
                
                # 提取模块名称
                if first_step:
                    # 计算有几个"-"
                    dash_count = first_step.count('-')
                    if dash_count >= 1:
                        # 获取第一个"-"之前的内容作为一级模块
                        primary_module = first_step.split('-', 1)[0].strip()
                        
                    if dash_count >= 2:
                        # 获取第一个"-"之后，第二个"-"之前的内容作为二级模块
                        remaining = first_step.split('-', 1)[1].strip()
                        secondary_module = remaining.split('-', 1)[0].strip()
                
                # 遍历该测试用例的所有断言详情
                for assertion in assertion_details:
                    assertion_name = assertion.get('name', '')
                    assertion_result = assertion.get('result', '')
                    assertion_details_text = assertion.get('details', '')

                    # 过滤掉name为"测试场景-xxx"的断言记录（这些仅用于标识测试场景）
                    if re.search(r'测试场景-(.+)', assertion_name):
                        continue

                    # 获取接口相关数据
                    interface_url = test_case.get('url', '')
                    interface_request = test_case.get('request_data', {})
                    interface_response = test_case.get('response_data', {})

                    # 将复杂对象转换为JSON字符串以便在Excel中显示
                    import json
                    try:
                        request_str = json.dumps(interface_request, ensure_ascii=False,
                                                 indent=2) if interface_request else ''
                    except:
                        request_str = str(interface_request)

                    try:
                        response_str = json.dumps(interface_response, ensure_ascii=False,
                                                  indent=2) if interface_response else ''
                    except:
                        response_str = str(interface_response)
                    if "code 等于 0000" in assertion_name:
                        assertion_name = '接口正常响应，数据结构未变动'
                    # 直接添加到Excel数据列表中，保持原始顺序
                    excel_row = {
                        '一级模块': primary_module,
                        '二级模块': secondary_module,
                        '测试场景名称': scenario_name,
                        '测试步骤': test_case_name,
                        '预期结果': assertion_name,
                        '实际结果': assertion_result,
                        '接口url': interface_url,
                        '接口请求参数': request_str,
                        '接口响应数据': response_str
                    }
                    excel_data.append(excel_row)

                    # 统计信息
                    scenario_stats[scenario_name] += 1

        if not excel_data:
            print("没有生成任何Excel数据")
            return

        # 创建DataFrame，保持原始顺序，不进行排序
        df = pd.DataFrame(excel_data)

        # 写入Excel文件
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='测试场景执行结果', index=False)

            # 获取工作表对象以进行格式化
            worksheet = writer.sheets['测试场景执行结果']

            # 设置列宽
            worksheet.column_dimensions['A'].width = 20  # 一级模块
            worksheet.column_dimensions['B'].width = 20  # 二级模块
            worksheet.column_dimensions['C'].width = 58  # 测试场景名称
            worksheet.column_dimensions['D'].width = 55  # 测试步骤
            worksheet.column_dimensions['E'].width = 30  # 预期结果
            worksheet.column_dimensions['F'].width = 12  # 实际结果
            worksheet.column_dimensions['G'].width = 50  # 接口url
            worksheet.column_dimensions['H'].width = 40  # 接口请求参数
            worksheet.column_dimensions['I'].width = 40  # 接口响应数据

            # 设置标题行样式
            # 标题行样式
            title_font = Font(bold=True, size=12)
            title_fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
            title_alignment = Alignment(horizontal='center', vertical='center')

            # 应用标题行样式
            for col in range(1, 10):  # A, B, C, D, E, F, G, H, I列
                cell = worksheet.cell(row=1, column=col)
                cell.font = title_font
                cell.fill = title_fill
                cell.alignment = title_alignment

            # 设置所有行高为40
            for row in range(1, len(df) + 2):  # 包括标题行和所有数据行
                worksheet.row_dimensions[row].height = 30

            # 设置数据行样式
            data_alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
            for row in range(2, len(df) + 2):
                for col in range(1, 10):  # A, B, C, D, E, F, G, H, I列
                    cell = worksheet.cell(row=row, column=col)
                    cell.alignment = data_alignment

        print(f"测试场景执行结果Excel文档已生成: {output_file}")
        print(f"共生成 {len(excel_data)} 条记录，涵盖 {len(scenario_stats)} 个测试场景")

        cases_pass_count = 0 # 测试用例通过数量
        for i in excel_data:
            if i['实际结果'] == "通过":
                cases_pass_count +=1
        cases_fail_count = len(excel_data) - cases_pass_count
        # 打印测试场景统计信息
        print("\n测试场景统计:")
        for scenario_name, count in scenario_stats.items():
            print(f"  {scenario_name}: {count} 个测试步骤")

        return  {'cases_count': len(excel_data),
                 'cases_pass_count': cases_pass_count,
                 'cases_fail_count': cases_fail_count,
                 'scenario_count':len(scenario_stats)}

    except Exception as e:
        print(f"生成Excel文档时出错: {str(e)}")


if __name__ == "__main__":
    main()

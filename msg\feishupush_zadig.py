# -*- coding:utf-8 -*-
# @Time: 2024/9/26 14:49
# @Author: wenjie.hu
# @Email: <EMAIL>

import sys
import ast
import os
import requests
import time
import datetime
import json
import traceback

# 动态添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)  # 获取项目根目录
if project_root not in sys.path:
    sys.path.insert(0, project_root)
from tool.parse_report import parse_apifox_report, generate_test_scenario_excel


def send_feishu():
    # 默认发送错误消息
    title_desc = f"[{CONF['APIFOX_CASE_NAME']}] 测试失败"
    title_font = "red"  # 标题颜色
    RESULT = "FAIL"
    # 打包报告模板
    payload = {}
    if apifox_result:
        # 显示基本信息
        basic_info = apifox_result.get('basic_info', {})
        # 显示统计信息
        statistics = apifox_result.get('statistics', {})
        # 用例PASS总数
        http_requests_pass = statistics.get('http_requests_total', 0) - statistics.get('http_requests_failed', 0)
        # 断言PASS总数
        assertions_pass = statistics.get('assertions_total', 0) - statistics.get('assertions_failed', 0)
        total_duration = str(basic_info.get('total_duration', 0))
        total_duration = total_duration.replace("ms","毫秒")
        total_duration = total_duration.replace("d", "天")
        total_duration = total_duration.replace("h", "时")
        total_duration = total_duration.replace("m","分")
        total_duration = total_duration.replace("s", "秒")
        case_pass_rate =  test_data.get('cases_pass_count', 0) / test_data.get('cases_count', 0)
        if case_pass_rate == 1:
            title_desc = f"[{CONF['APIFOX_CASE_NAME']}] 测试通过"
            title_font = "green"  # 标题颜色
            RESULT = "PASS"
        case_pass_rate = f"{round(case_pass_rate*100,2)}%"
        payload = json.dumps({
            "msg_type": "interactive",
            "card": {
                "type": "template",
                "data": {
                    "template_id": "AAqIwmgluyPLx",
                    "template_version_name": "1.0.0",
                    "template_variable": {
                        "test_scenario": str(basic_info.get('test_scenario', 'N/A')),
                        "run_time": str(basic_info.get('run_time', 'N/A')),
                        "JOB_NAME": CONF['JOB_NAME'],
                        "GIT_BRANCH": CONF['GIT_BRANCH'],
                        "GIT_COMMIT": CONF['GIT_COMMIT'],
                        "run_tool": basic_info.get('run_tool', 'N/A'),
                        "api_request_duration": str(basic_info.get('api_request_duration', 'N/A')),
                        "avg_api_request_duration": str(basic_info.get('avg_api_request_duration', 'N/A')),
                        "http_requests_total": str(statistics.get('http_requests_total', 'N/A')),
                        "total_duration": total_duration,
                        "pass_rate": case_pass_rate,
                        "DOW_URL": {
                            "pc_url": CONF.get('LOG_URL', ''),
                            "android_url": CONF.get('LOG_URL', ''),
                            "ios_url": CONF.get('LOG_URL', ''),
                            "url": CONF.get('LOG_URL', '')
                        },
                        "LOG_URL": {
                            "pc_url": CONF.get('LOG_URL', ''),
                            "android_url": CONF.get('LOG_URL', ''),
                            "ios_url": CONF.get('LOG_URL', ''),
                            "url": CONF.get('LOG_URL', '')
                        },
                        "table_raw_array": [
                            {
                                "name": "用例总数",
                                "fail_num": str(test_data.get('cases_fail_count', 'N/A')),
                                "pass_num": str(test_data.get('cases_pass_count', 'N/A')),
                                "total_num": str(test_data.get('cases_count', 'N/A'))
                            },
                            {
                                "name": "接口总数",
                                "fail_num": str(statistics.get('http_requests_failed', 'N/A')),
                                "pass_num": str(http_requests_pass),
                                "total_num": str(statistics.get('http_requests_total', 'N/A'))
                            },
                            {
                                "name": "断言总数",
                                "fail_num": str(statistics.get('assertions_failed', 'N/A')),
                                "pass_num": str(assertions_pass),
                                "total_num": str(statistics.get('assertions_total', 'N/A'))
                            }
                        ],
                        "RESULT": RESULT,
                        "title_desc": title_desc,
                        "RESULT_COLOR": title_font,
                        'cases_count': str(test_data.get('cases_count', 'N/A'))
                    }
                }
            }
        })
    # 发送飞书测试群消息
    headers = {
        'Content-Type': 'application/json'
    }
    requests.request(method="post", url=url_test, headers=headers, data=payload)

if __name__ == '__main__':
    try:
        CONF = ast.literal_eval(sys.argv[1])
        print(CONF)
        url_test = 'https://open.feishu.cn/open-apis/bot/v2/hook/31e1a930-130c-4987-a7db-7465c2c443fc'  # 自动群机器人API
        # 解析apifox的html报告数据
        # 服务器路径
        apifox_result = parse_apifox_report(f"./apifox-reports/{CONF['PROJECT_NAME']}_{CONF['BACKEND_NAME']}_report.html")
        test_data = generate_test_scenario_excel(apifox_result,f"./apifox-reports/{CONF['PROJECT_NAME']}_{CONF['BACKEND_NAME']}_report.xlsx")
        send_feishu()
    except Exception as e:
        print(f"发生飞书消息错误:{traceback.format_exc()}")
        # send_err_msg()

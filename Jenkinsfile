pipeline {
    agent any // 使用任意可用的代理进行构建

    // parameters { // 代码里配置相关选择项不能灵活固定默认值，暂时还是从jenkins网页配置！
    //     choice(name: 'appEnv', choices: ['测试环境', '灰度环境', '正式环境'], description: '【请选择环境】') // 选择参数
    //     string(name: 'VERSION', defaultValue: '1.0', description: 'Enter the version number to use') // 字符串参数
    // }

    environment {
        // 全局环境变量
        NODEJS_VER = 'node_16.16.0' // 默认nodejs版本-对应jenkins网页-Tools配置里的nodejs安装名称跟路径
        // NODEJS_VER = '18.1.0' // 默认nodejs版本
        REPORT_PATH = "/apps/nginx/html/AutoApiTest/reports/" // ftp存储测试报告路径
        CREDENTIALSID = '54f89729-386c-40ed-962e-92a4d957747a' // credentialsId密匙
        GIT_TAG = "" // git短tag
        GIT_LOG = "" // 倒数第1行git提交日志
        GIT_BRANCH = "HEAD" // git分支
        GIT_COMMIT = ""
        GIT_COMMIT_TIME = ""
        PROJECT_NAME = "" // 项目名称
        PRODUCT_NAME = "" // 产品名称
        BACKEND_NAME = "" // 后端服务名称
        PRODUCT_MODULE = "" // 产品模块
    }

    tools {nodejs "${NODEJS_VER}"} // nodejs工具名称

    options {
        // ansiColor('xterm') // 启用控制台颜色
        // dir "${NEW_WORKSPACE_PATH}/bulidDir" // 在这里定义全局工作目录
        buildDiscarder(logRotator(numToKeepStr: '100')) // 保留最近的12个构建日志
        // disableConcurrentBuilds() // 禁止并发构建
        // timeout(time: 1, unit: 'HOURS') // 设置构建超时时间为1小时
    }

    stages {// 定义阶段及其执行顺序

        stage('拉取git分支最新代码') { // 获取代码阶段
                steps {
                    script {
                        try {
                            // 可通过jenkins网页构建任务传入giturl路径和gitBranch分支
                            echo "gitBranch分支:${params.GIT_BRANCH}"
                            // 指定git分支
                            def p_gitBranch = params.GIT_BRANCH
                            if (params.GIT_BRANCH){
                                GIT_BRANCH = p_gitBranch
                                p_gitBranch = p_gitBranch.replaceAll("\\s+", "")
                                // 切换指定分支
                                checkout([$class: 'GitSCM', branches: [[name: "${p_gitBranch}"]],
                                doGenerateSubmoduleConfigurations: false,
                                extensions: [],
                                submoduleCfg: [],
                                userRemoteConfigs: [[credentialsId: "${CREDENTIALSID}", url: "http://git.allsaintsmusic.com:8888/auto_test/AutoApiTest.git"]]])
                            }


                            // 如果存在commitID，则切换指定commitID              
                            def p_commitId = params.commitId
                            if (p_commitId) {
                                // 拉取指定commitID版本
                                echo "拉取指定commitID版本"
                                echo "git commitID:${p_commitId}"
                                sh "git reset --hard ${p_commitId}" // 切换到指定的 commit ID
                            }

                            // 获取git代码提交信息
                            GIT_TAG = sh(returnStdout: true, script: 'git rev-parse --short HEAD').trim()
                            // 判断GIT_TAG位数是否超过7位
                            if (GIT_TAG && GIT_TAG.size() > 7) {
                                GIT_TAG = GIT_TAG[0..6]  // 截取前7位
                            }
                            GIT_COMMIT = sh(returnStdout: true, script: 'git rev-parse HEAD').trim()
                            GIT_LOG = sh(returnStdout: true, script: '''git log --pretty=format:"%s" ${GIT_COMMIT} -1 | sed 's/\\"//g' | sed "s/'\\"'/\\\\\'/g"''').trim()                    
                            GIT_COMMIT_TIME = sh(returnStdout: true, script: "git log --pretty=format:%at -n 1 ${GIT_COMMIT}").trim()
                            echo "GIT_LOG:${GIT_LOG}"
                            echo "GIT_TAG:${GIT_TAG}"
                            echo "GIT_COMMIT:${GIT_COMMIT}"
                            echo "GIT_COMMIT_TIME：${GIT_COMMIT_TIME}"
                        } catch (Exception e) {
                            echo "拉取git分支最新代码错误: ${e}"
                            currentBuild.result = 'FAILURE'
                            error "拉取git分支最新代码失败: ${e.message}"
                        }
                    }
                }
            }
            

        stage('安装ApifoxCLI插件') {
            steps {
                script {
                    try {
                        // 获取本地安装的 apifox-cli 版本
                        def localVersionCmd = "apifox -v || echo 'not installed'"
                        def localVersion = sh(script: localVersionCmd, returnStdout: true).trim()
                        
                        // 获取 npm 仓库中的最新版本
                        def latestVersionCmd = "npm view apifox-cli version --registry=https://registry.npmmirror.com/"
                        def latestVersion = sh(script: latestVersionCmd, returnStdout: true).trim()
                        
                        // 检查是否需要安装
                        if (localVersion == 'not installed') {
                            echo "Apifox CLI 未安装，正在安装最新版本 ${latestVersion}..."
                            sh 'npm i -g apifox-cli@latest --registry=https://registry.npmmirror.com/'
                        } else if (!localVersion.contains(latestVersion)) {
                            echo "Apifox CLI 需要更新，本地版本: ${localVersion}，最新版本: ${latestVersion}"
                            sh 'npm i -g apifox-cli@latest --registry=https://registry.npmmirror.com/'
                        } else {
                            echo "Apifox CLI 已是最新版本 ${localVersion}，无需重新安装"
                        }                        // 获取本地安装的 apifox-cli 版本
                    } catch (Exception e) {
                            echo "安装ApifoxCLI插件错误: ${e}"
                            currentBuild.result = 'FAILURE'
                            error "安装ApifoxCLI插件失败: ${e.message}"
                    }
                }
            }
        }

        stage('同步python依赖包') {
            steps {
                script {
                    try {
                        // sh 'uv venv && source .venv/bin/activate && uv sync'
                        sh 'pip3 install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/'
                    } catch (Exception e) {
                        echo "同步python依赖包错误: ${e}"
                        currentBuild.result = 'FAILURE'
                        error "同步python依赖包失败: ${e.message}"
                    }
                }
            }
        }

        stage('执行ApiFox测试场景') {
            steps {
                script {
                    sh "apifox -v" // 查看apifox版本

                    def backendNames = []
                    if (params.BACKEND_NAME) {
                        backendNames = params.BACKEND_NAME.split(',').collect { it.trim() }.findAll { it }
                    }

                    if (params.PROJECT_NAME && !backendNames.isEmpty()) {
                        for (backendName in backendNames) {
                            echo "==================== 开始处理后端服务: ${backendName} ===================="
                            
                            def hostsModified = false
                            def testPassed = true // 默认测试通过
                            try {
                                // 检查是否是OPPO华为云灾备环境测试
                                if (backendName == "OPPO灾备环境主流程验证") {
                                    echo "检测到 [${backendName}]，需要临时修改hosts文件"
                                    
                                    // 备份原hosts文件
                                    sh "sudo cp /etc/hosts /etc/hosts.bak"
                                    
                                    // 添加临时hosts记录-指向华为云灾备测试环境的LB的IP
                                    sh '''
                                        sudo tee -a /etc/hosts << EOF
************     api-server.allsaints.top
************     oppo-mbi.allsaintsmusic.com 
************     msg2-oppo-mbi.allsaints.top 
************     m-api-server.allsaints.top 
************     msg2-mi-mbi.allsaints.top
EOF
                                    '''
                                    echo "已为 [${backendName}] 添加临时hosts记录"
                                    hostsModified = true
                                }
                                
                                // 执行ApiFox测试场景
                                echo "=========== 开始执行 [${backendName}] 测试场景 ==========="
                                sh "apifox run examples/${params.PROJECT_NAME}/${backendName}.apifox-cli.json -r cli,html --verbose --out-file ${params.PROJECT_NAME}_${backendName}_report_${BUILD_NUMBER}"
                                echo "=========== [${backendName}] 测试场景执行完毕 ==========="

                            } catch (Exception e) {
                                echo "执行 [${backendName}] 测试场景错误: ${e}"
                                currentBuild.result = 'FAILURE' // 标记整个构建为失败
                                testPassed = false // 标记当前服务测试失败
                            } finally {
                                // 无论测试成功还是失败，都归档HTML报告
                                archiveArtifacts artifacts: "apifox-reports/${params.PROJECT_NAME}_${backendName}_report_${BUILD_NUMBER}.html", allowEmptyArchive: true, onlyIfSuccessful: false
                                
                                // 如果为当前后端修改了hosts文件，测试完成后恢复
                                if (hostsModified) {
                                    echo "恢复 [${backendName}] 的原hosts文件"
                                    sh "sudo cp /etc/hosts.bak /etc/hosts"
                                    sh "sudo rm /etc/hosts.bak"
                                }
                            }

                            // 为当前服务推送飞书结果并归档Excel报告
                            def resultStatus = testPassed ? "PASS" : "FAIL"
                            def resultDesc = testPassed ? "测试通过！" : "测试不通过，详情可查看执行日志/测试报告！"
                            def CONF = """
                            {
                                "JOB_URL":"${JOB_URL}",
                                "JOB_NAME":"${JOB_NAME}",
                                "JOB_BASE_NAME":"${JOB_BASE_NAME}",
                                "BUILD_NUMBER":"${BUILD_NUMBER}",
                                "BUILD_URL":"${BUILD_URL}",
                                "GIT_BRANCH":"${GIT_BRANCH}",
                                "GIT_TAG":"${GIT_TAG}",
                                "GIT_LOG":"${GIT_LOG}",
                                "GIT_COMMIT_TIME":"${GIT_COMMIT_TIME}",
                                "GIT_COMMIT":"${GIT_COMMIT}",
                                "APIFOX_CASE_NAME":"${params.PROJECT_NAME}_${backendName}",
                                "RESULT":"${resultStatus}",
                                "PROJECT_NAME":"${params.PROJECT_NAME}",
                                "BACKEND_NAME":"${backendName}",
                                "RESULT_DESC":"${resultDesc}"
                            }
                            """
                            CONF = CONF.replaceAll("\\s", "") // 去除空格跟换行符
                            env.CONF = CONF
                            echo CONF

                            try {
                                sh "sudo python3 ./msg/feishupush.py '${CONF}'"
                            } catch (Exception e) {
                                echo "为 [${backendName}] 推送飞书消息时出错: ${e}"
                            } finally {
                                // 归档Excel报告
                                archiveArtifacts artifacts: "apifox-reports/${params.PROJECT_NAME}_${backendName}_case_${BUILD_NUMBER}.xlsx", allowEmptyArchive: true, onlyIfSuccessful: false 
                            }
                            
                            echo "==================== 后端服务: ${backendName} 处理完毕 ===================="
                        }
                    }
                }
            }
        }
    }

    post { // 在所有阶段执行完后进行的操作，包括构建结果的判断和通知等

            // always {
            //     deleteDir() // 删除工作目录
            // }

            success {
                echo '构建成功!' // 在控制台显示构建成功的消息

            }

            failure {
                script {
                    echo '构建失败了。详细的失败信息已在各服务测试阶段的飞书通知中发送。'
                    // 归档所有报告作为备份
                    def backendNames = []
                    if (params.BACKEND_NAME) {
                        backendNames = params.BACKEND_NAME.split(',').collect { it.trim() }.findAll { it }
                    }
                    if (params.PROJECT_NAME && !backendNames.isEmpty()) {
                        echo "归档所有HTML报告..."
                        for (backendName in backendNames) {
                            archiveArtifacts artifacts: "apifox-reports/${params.PROJECT_NAME}_${backendName}_report_${BUILD_NUMBER}.html", allowEmptyArchive: true, onlyIfSuccessful: false
                        }
                        echo "归档所有Excel报告..."
                        for (backendName in backendNames) {
                            archiveArtifacts artifacts: "apifox-reports/${params.PROJECT_NAME}_${backendName}_case_${BUILD_NUMBER}.xlsx", allowEmptyArchive: true, onlyIfSuccessful: false 
                        }
                    }
                }
            }

            unstable {
                echo '构建状态不稳定!' // 在控制台显示构建状态不稳定的消息
            }

            changed {
                echo '代码被修改!' // 在控制台显示代码被修改的消息
            }
        }
    
}

def copyArtifacts(pattern, from, to) {
    // 复制公共方法
    // 先判断是否有文件再复制
    def file_name = "${from}/${pattern}"
    try {
        // 使用 returnStatus 而不是 returnStdout 来避免因 grep 未找到匹配而失败
        def findStatus = sh(script: "find . -name '${pattern}' | grep -i '${from}'", returnStatus: true)
        if (findStatus == 0) {
            // 支持批量同名复制，如果有同名文件会自动加上一个数字后缀
            sh "find ${from} -name '${pattern}' -exec cp -n {} ${to} \\;"
        } else {
            echo "提示：${from}目录下不存在${pattern}文件,暂不执行复制操作！"
        }
    } catch (Exception e) {
        echo "复制[${file_name}]错误: ${e}"
    }

}
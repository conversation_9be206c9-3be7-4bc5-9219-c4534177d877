# -*- coding:utf-8 -*-
# @Time: 2024/8/25 12:34
# @Author: wenjie.hu
# @Email: <EMAIL>
# 直接读取csv文件。批量生产access_token给wrk脚本调用

import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
import time
import csv

# 替换为您的密钥和初始化向量
# key = b"NqjpTqqcSxBvppnA"  # 密钥
# iv = b"HdxwgNLkJwILyhZA"  # 初始化向量


# 测试环境
key = b"HdxwgNLkJwILyhZI"  # 密钥
iv = b"NqjpTqqcSxBvppnc"  # 初始化向量

def generate_access_token(uid):
    timestamp = int(time.time() * 1000)
    data_str = f"{uid}|{timestamp}".encode("utf-8")
    cipher = AES.new(key, AES.MODE_CBC, iv)
    ciphertext = cipher.encrypt(pad(data_str, AES.block_size))
    hex_ciphertext = ciphertext.hex()
    access_token = base64.urlsafe_b64encode(bytes.fromhex(hex_ciphertext))
    return access_token.decode("utf-8")


# 读取 hw_data.csv 文件,跳过第一行(标题行)
with open('hw_data.csv', 'r') as file:
    reader = csv.reader(file, skipinitialspace=True)
    data = list(reader)
    header = data[0]  # 保存标题行
    data = data[1:]  # 跳过标题行

# 检查是否有数据
if not data:
    print("hw_data.csv 文件中没有有效数据行。")
else:
    # 遍历数据并生成 access_token
    new_data = []
    for row in data:
        uid, pid = row
        access_token = generate_access_token(uid)
        new_row = [uid, pid, access_token]
        new_data.append(new_row)

    # 将新数据写入新文件 new_hw_data.csv
    with open('new_hw_data.csv', 'w', newline='') as file:
        writer = csv.writer(file)
        writer.writerow(['uid', 'pid', 'access_token'])  # 写入标题行
        writer.writerows(new_data)
    print("新文件 new_hw_data.csv 已生成。")

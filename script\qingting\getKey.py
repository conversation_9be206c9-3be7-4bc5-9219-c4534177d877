# -*- coding:utf-8 -*-
# @Time: 2024/7/31 14:46
# @Author: wenjie.hu
# @Email: <EMAIL>
# 接收蜻蜓长音频更新通知  nacos配置文件：podcaster-importer-test.yml
import sys
import hashlib


def generate_qt_sign(content, client_secret):
    """
    根据请求内容和客户端秘钥生成QT-Sign签名

    参数:
    content (str): 请求体内容
    client_secret (str): 客户端秘钥

    返回:
    str: QT-Sign签名值
    """
    sign_content = content + client_secret
    sign_content_bytes = sign_content.encode('utf-8')
    md5_digest = hashlib.md5(sign_content_bytes).digest()
    qt_sign = md5_digest.hex().upper()
    return qt_sign


if __name__ == '__main__':
    # 使用示例
    content = sys.argv[1]  # 获取脚本传入的body内容
    apifox_env = sys.argv[2] # 获取apifox环境变量(是正式还是测试环境)
    # content = '{"data":{"id":242222},"type":"channel_offline"}'
    client_secret = 'NTdjM2U3ODUtNTgyZi0zOWY4LTlmOWQtMDY0YmFmNmNlZDI0' # 默认测试环境key
    if apifox_env == "prod": # 如果是prod，则切换为正式环境key
        client_secret = 'Y2M4MDhjZmMtNmUyZi0zOGJiLThhZDgtMmU4NzgzYWM4Yjdm' 
    qt_sign = generate_qt_sign(content, client_secret)
    res = {'QT-Sign': qt_sign}
    print(res)

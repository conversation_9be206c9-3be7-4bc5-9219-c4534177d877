{"apifoxCli": "1.1.0", "item": [{"item": [{"id": "778ae2be-19db-4c74-a5cc-8ae4cab1b098", "name": "EAP获取今日app日活数据", "request": {"url": {"protocol": "http", "port": "5818", "path": ["eap", "getTodayData"], "host": ["211", "139", "189", "130"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://***************:5818", "body": {"mode": "raw", "raw": "{\n    \"appInfo\": [\n        {\n            \"name\": \"com.heytap.music\",\n            \"versionList\": [\n                \"40.10.14.0_dd2ddea_231123\"\n            ]\n        }\n    ]\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`测试场景-EAP本地服务状态检测`);", "      pm.test(formattedName, async function(done) {", "        try {", "          const value = pm.response.text();", "          ", "    const compareValue = await ____replaceIn(\"\");", "    const formattedValues = ____formatValues(value, compareValue, 'exists');", "  ", "          pm.expectWithKey({key:'undefined',value: value === null ? '' : formattedValues.value }).to.exist", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.code 等于 200`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.code`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`200`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.2.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.data.activeUser 存在 `);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data.activeUser`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(\"\");", "    const formattedValues = ____formatValues(value, compareValue, 'exists');", "  ", "          pm.expectWithKey({key:'$.data.activeUser',value: value === null ? '' : formattedValues.value }).to.exist", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}], "responseDefinition": {"id": 697382166, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "oasExtensions": "", "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"appInfo": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "versionList": {"type": "array", "items": {"type": "string"}}}, "required": ["name", "versionList"]}}}, "required": ["appInfo"]}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "778ae2be-19db-4c74-a5cc-8ae4cab1b098", "id": "778ae2be-19db-4c74-a5cc-8ae4cab1b098", "type": "http", "name": "EAP获取今日app日活数据", "projectId": 4800107, "relatedId": 6733562, "environmentId": 24915297, "blockNumber": 1, "httpApiId": 308606625, "httpApiCaseId": 271376719, "httpApiName": "EAP获取今日app日活数据", "httpApiPath": "/eap/getTodayData", "httpApiMethod": "post", "httpApiCaseName": "EAP获取今日app日活数据"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}], "name": "EAP本地服务状态检测"}], "info": {"name": "EAP本地服务状态检测"}, "dataSchemas": {}, "mockRules": {"rules": [], "enableSystemRule": true}, "environment": {"id": 24915297, "name": "测试环境", "baseUrl": "https://test-upmp.allsaints.group", "baseUrls": {"default": "https://test-upmp.allsaints.group", "308c62cb-9cc2-418f-bb3b-974a33462e34": "https://test-upmp.allsaints.group", "a47196e6-7551-487d-b543-232e55e09683": "https://test-api-server.allsaints.top", "21211e65-3c46-4c63-b963-7a7a65929263": "https://test-api-oppo-bgm.allsaints.top", "d840ff7e-32d9-4497-8809-f2f124dde270": "https://test-logger-web.allsaints.group", "7bd03f4f-8d2d-47f1-b108-ba5e5bffdbfa": "http://*************:8080", "5c1c2eb9-bcf3-4222-95ba-8f635ec27bb3": "http://10.171.3.96:8080", "8a734824-b5a5-4c36-bdf0-233a7d3dfe54": "http://10.171.2.133:8080", "c13fae2a-5dd2-4045-b574-725d20ccf2d6": "http://gw-cms.allsaints-internal.com", "84bc06a8-d32b-48b7-b0e2-9ed78b05147c": "", "a6d5521c-eb4f-416c-ba0d-2cec8a5b9525": "https://test-local-push.allsaints.top", "48734eb2-f8a6-4d94-ba7c-8f480ed34ac7": "https://test-h5-oppo-mbi.allsaints.top", "5266cc13-50f6-459a-b0b4-e9bd4b016e4d": "https://test-welfare-api.allsaintsmusic.com", "88eb862b-ac30-4d66-bf76-a640e6a8f1d0": "https://test-pay-oppo.allsaints.top", "2e740854-ab40-46d2-a3fa-a9b2fcc88b0a": "https://test-music-platform-ui.allsaints.group", "ec7be0b3-1eb9-4f73-aaae-f4aa5212b816": "gw-reco.allsaints-internal.com", "eb35bc81-9704-4e80-9e55-c5f1a2fc66b2": "https://test-h5-oppo-mbi.allsaints.top", "7c318b03-a1c8-4f5c-bfe7-9f4266835e50": "https://test-rms.allsaints.group", "7441c433-7298-4d9c-9e9a-1850c7d97541": "https://test-ringtone.allsaints.top/", "91dcd344-c529-42bc-aabd-7859db7a4d99": "https://test-mms.allsaints.top", "7d8f72ab-e100-445b-aee5-bc950abe3539": "http://***************:5818"}, "variable": {"id": "92085087-68d2-4427-9a9c-5d4d017a06ab", "name": "测试环境", "values": [{"type": "any", "value": "test", "key": "env", "isBindInitial": true, "initialValue": "test"}, {"type": "any", "value": "292", "key": "uid", "isBindInitial": true, "initialValue": "292"}, {"type": "any", "value": "CN", "key": "region", "isBindInitial": true, "initialValue": "CN"}, {"type": "any", "value": "1001", "key": "channel", "isBindInitial": true, "initialValue": "1001"}, {"type": "any", "value": "cmsAutoTest", "key": "username", "isBindInitial": true, "initialValue": "cmsAutoTest"}, {"type": "any", "value": "qQxYlm+pacFXjDi8Kxul7+0Ub3cNDnYh6Hx5xDg=", "key": "password", "isBindInitial": true, "initialValue": "qQxYlm+pacFXjDi8Kxul7+0Ub3cNDnYh6Hx5xDg="}, {"type": "any", "value": "C:\\Users\\<USER>\\AppData\\Roaming\\apifox\\ExternalPrograms\\examples/oppo/data/songList_picture.jpg", "key": "fileName", "isBindInitial": false, "initialValue": ""}, {"type": "any", "value": "5ehINSkrV7iaFi2tLjle65HbFZW8NVlD6PvpBhoKKuVKTUSm6oeVQ77Fgmq-JsYFDpYhpm-oLmZTQfPUFnSH4gqaMQFtZROjfFj_vWoiry86VrJnSh4Vx0sPlzUINYqL", "key": "access_token", "isBindInitial": false, "initialValue": ""}, {"type": "any", "value": "EO4DoXfSxRRv6wPha9wRR3_AAE_EuGP-2-TfxzAQbhgLq7O_4xtyik8arR2k8eYvUpm67jTXChRHLDHwrtWqN62B3yyYPOySC79sAKRZexns_9DGQRu-lyHoJrLW5cvT", "key": "refresh_token", "isBindInitial": false, "initialValue": ""}, {"type": "any", "value": "undefined", "key": "vip_time", "isBindInitial": false, "initialValue": ""}, {"type": "any", "value": "", "key": "bgm_headers", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "bgm_appid", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "ultimate_headers", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "qingting_headers", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "ximalaya_body", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "variable_key", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "Apple", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "labelname", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "金刚区的栏目自动化测试,彩铃专区,每日推荐,移动会员线上,歌手,移动会员,免费畅听,猜你喜欢,音乐电台,短视频,热门专区,金刚区栏目名称测试验证123金刚区栏目名称测试验证123金刚区栏", "key": "allLabels", "isBindInitial": false, "initialValue": ""}, {"type": "any", "value": ["测试", "广告栏目", "金刚区", "私人专属好歌", "为你推荐今日歌单", "替换今日歌单", "私人专属歌单", "排行榜", "这是默认栏目名", "更多为你推荐"], "key": "tabLabel", "isBindInitial": false, "initialValue": ""}, {"type": "any", "value": 71156, "key": "jgq_id", "isBindInitial": false, "initialValue": ""}, {"type": "any", "value": "", "key": "获取token", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "daily", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "每日推荐", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "Daily Recommendation", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "coin_amount", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "appMessageId", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "ids", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "coin_available", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "提取蜻蜓购买订单号", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "提取喜马拉雅购买订单号", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "提取订单号", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "tiqubianliang", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "提取马拉雅购买订单号", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "会员购买-拉起支付", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "提取会员购买订单号", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "单曲购买-提取订单号", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "utoken", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "ai_utoken", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "api_utoken", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "a", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "45972193", "key": "songId", "isBindInitial": false, "initialValue": ""}, {"type": "any", "value": "测试CRMS的同步系统", "key": "songName", "isBindInitial": false, "initialValue": ""}, {"type": "any", "value": "", "key": "id", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "imgTest", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "78605", "key": "idtest", "isBindInitial": true, "initialValue": "78605"}]}, "requestProxyAgentSettings": {"default": {"agentId": 2}, "308c62cb-9cc2-418f-bb3b-974a33462e34": {"agentId": 2}, "a47196e6-7551-487d-b543-232e55e09683": {"agentId": 2}, "21211e65-3c46-4c63-b963-7a7a65929263": {"agentId": 2}, "d840ff7e-32d9-4497-8809-f2f124dde270": {"agentId": 2}, "7bd03f4f-8d2d-47f1-b108-ba5e5bffdbfa": {"agentId": 2}, "5c1c2eb9-bcf3-4222-95ba-8f635ec27bb3": {"agentId": 2}, "8a734824-b5a5-4c36-bdf0-233a7d3dfe54": {"agentId": 2}, "c13fae2a-5dd2-4045-b574-725d20ccf2d6": {"agentId": 2}, "84bc06a8-d32b-48b7-b0e2-9ed78b05147c": {"agentId": 2}, "a6d5521c-eb4f-416c-ba0d-2cec8a5b9525": {"agentId": 2}, "5266cc13-50f6-459a-b0b4-e9bd4b016e4d": {"agentId": 2}, "48734eb2-f8a6-4d94-ba7c-8f480ed34ac7": {"agentId": 2}, "88eb862b-ac30-4d66-bf76-a640e6a8f1d0": {"agentId": 2}, "2e740854-ab40-46d2-a3fa-a9b2fcc88b0a": {"agentId": 2}, "ec7be0b3-1eb9-4f73-aaae-f4aa5212b816": {"agentId": 2}, "eb35bc81-9704-4e80-9e55-c5f1a2fc66b2": {"agentId": 2}, "7c318b03-a1c8-4f5c-bfe7-9f4266835e50": {"agentId": 2}, "7441c433-7298-4d9c-9e9a-1850c7d97541": {"agentId": 2}, "91dcd344-c529-42bc-aabd-7859db7a4d99": {"agentId": 2}, "343c36aa-**************-6b81d2fbd9b9": {"agentId": 2}, "7d8f72ab-e100-445b-aee5-bc950abe3539": {"agentId": 2}}, "type": "normal", "parameter": {"header": [], "query": [], "body": [], "cookie": []}}, "globals": {"baseUrl": "", "baseUrls": {}, "variable": {"id": "6d65557e-0367-4ffc-bf16-a986bda25012", "values": [{"type": "any", "value": "", "key": "access_token", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "uid", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "region", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "channel", "isBindInitial": true, "initialValue": ""}]}, "parameter": {"header": [], "query": [], "body": [], "cookie": []}}, "isServerBuild": false, "isTestFlowControl": false, "projectOptions": {"enableJsonc": false, "enableBigint": false, "responseValidate": true, "isDefaultUrlEncoding": 2, "enableTestScenarioSetting": false, "enableYAPICompatScript": false, "publishedDocUrlRules": {"defaultRule": "RESOURCE_KEY_ONLY", "resourceKeyStandard": "LEGACY"}, "folderShareExpandModeSettings": {"expandId": [], "mode": "AUTO"}, "mockSettings": {"engine": "fakerjs"}, "language": "zh-CN"}}
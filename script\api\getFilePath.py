
import os
import sys

#生成图片的绝对路径
def create_path(img_relative_path):
    #该文件目录路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    #该目录路径的上上级路径
    project_root = os.path.dirname(os.path.dirname(current_dir))
    relative_path = img_relative_path
    #把目录路径的上上级路径与图片的相对路径结合形成图片的绝对路径
    absolute_path = os.path.join(project_root, relative_path)
    return absolute_path

#图片的相对路径
img_relative_path = sys.argv[1]
#输出给apifox
print(create_path(img_relative_path))


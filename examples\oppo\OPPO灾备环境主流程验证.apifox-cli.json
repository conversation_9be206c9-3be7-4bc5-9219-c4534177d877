{"apifoxCli": "1.2.21", "item": [{"item": [{"id": "be02b6d4-f51d-5714-a768-9ae23c70b879", "type": "group", "metaInfo": {"id": "be02b6d4-f51d-5714-a768-9ae23c70b879", "type": "group", "scopeType": "start", "scopeEndId": "a9426357-b612-5fbd-b69c-e29e72f41547", "name": "root", "onError": "ignore"}}, {"id": "1643688f-752c-529b-a751-a0f89e5b1266", "name": "新广告-获取广告配置成功", "request": {"url": {"protocol": "https", "path": ["api", "v2", "advertisement", "config"], "host": ["api-server", "allsaints", "top"], "query": [{"disabled": false, "key": "appCode", "value": "101"}, {"disabled": false, "key": "userStatus", "value": "1"}, {"disabled": false, "key": "version", "value": "10.16.70"}, {"disabled": false, "key": "buildVersion", "value": "40.10.16.70test_a5e6ac9_241107"}, {"disabled": false, "key": "ak", "value": "test"}, {"disabled": false, "key": "nonce", "value": "763363c3386a486299887e1ecbf2ee28"}, {"disabled": false, "key": "ts", "value": "1731314648737"}, {"disabled": false, "key": "sign", "value": "8f2ac9b86ecb30cdb51ecb989a82fadae47e5718"}], "variable": []}, "header": [{"disabled": false, "key": "dev-build-version", "value": "40.10.16.70test_a5e6ac9_241107"}, {"disabled": false, "key": "dev-brand", "value": "OPPO"}, {"disabled": false, "key": "dev-manufacturer", "value": "OPPO"}, {"disabled": false, "key": "dev-model", "value": "PEPM00"}, {"disabled": false, "key": "dev-language", "value": "zh_cn"}, {"disabled": false, "key": "dev-region", "value": "cn"}, {"disabled": false, "key": "dev-timezone", "value": "Asia/Shanghai"}, {"disabled": false, "key": "dev-app-version", "value": "101016070"}, {"disabled": false, "key": "dev-channel", "value": "1001"}, {"disabled": false, "key": "dev-package", "value": "com.heytap.music"}, {"disabled": false, "key": "dev-id", "value": "d41d8cd98f00b204e9800998ecf8427e"}, {"disabled": false, "key": "dev-pid", "value": "491e0e49f88e473390b6e163fc34cad4"}, {"disabled": false, "key": "dev-ab-experiment", "value": "rCrNaEgHoxepLr327UJLUtMIcsTeLJJaEZ8Ag6s4P5y2QI9znEfhF5SlTPtMTpEp-9tf-CRbodUvoF_4rNmDe3FeQtEiEXgDViO4JXM6iA6trou0LWNB77DtMw3wljsM"}, {"disabled": false, "key": "dev-distinct-id", "value": "5460ae3d-4e4e-408a-9427-5a30824de0cf"}, {"disabled": false, "key": "dev-android-release-version", "value": "9"}, {"disabled": false, "key": "dev-android-version", "value": "28"}, {"disabled": false, "key": "dev-net", "value": "WIFI"}, {"disabled": false, "key": "dev-ou-id", "value": ""}, {"disabled": false, "key": "dev-du-id", "value": ""}, {"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}], "method": "GET", "baseUrl": "https://api-server.allsaints.top", "body": {}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.code 等于 0000`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.code`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`0000`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.data.configNew.configs 不为空 `);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data.configNew.configs`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "          ", "        if(value === null){", "          pm.expect(value).to.not.be.null", "        } else if(value === undefined) {", "          pm.expect(value).to.not.be.undefined", "        } else {", "          pm.expect(String(value)).to.not.be.empty", "        }", "      ", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}], "responseDefinition": {"id": 562275823, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "oasExtensions": "", "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "a6ccb004-0666-49dd-9b53-072bd01d50a9", "id": "1643688f-752c-529b-a751-a0f89e5b1266", "type": "http", "name": "新广告-获取广告配置成功", "projectId": 4800107, "relatedId": 6362013, "environmentId": 32208946, "blockNumber": 15, "httpApiId": 232301517, "httpApiCaseId": 255348596, "httpApiName": "新广告-获取广告配置", "httpApiPath": "/api/v2/advertisement/config", "httpApiMethod": "get", "httpApiCaseName": "新广告-获取广告配置"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "d926dc91-b91c-57ce-a3b2-baf3bfe98119", "name": "app旧广告-获取配置信息成功", "request": {"url": {"protocol": "https", "path": ["api", "v2", "setting", "advertisementConfig"], "host": ["api-server", "allsaints", "top"], "query": [{"disabled": false, "key": "version", "value": "10.16.20"}, {"disabled": false, "key": "buildVersion", "value": "50.10.16.20test_f164d81_240708"}, {"disabled": false, "key": "ak", "value": "test"}, {"disabled": false, "key": "nonce", "value": "34f0e059cb3f45a8ac2249eb24eb941d"}, {"disabled": false, "key": "ts", "value": "1744700694265"}, {"disabled": false, "key": "sign", "value": "62789d82a570bdf510a8e5f6ccfd641ff8b45dce"}], "variable": []}, "header": [{"disabled": false, "key": "dev-build-version", "value": "50.10.16.20test_f164d81_240708"}, {"disabled": false, "key": "dev-brand", "value": "OPPO"}, {"disabled": false, "key": "dev-manufacturer", "value": "OPPO"}, {"disabled": false, "key": "dev-model", "value": "PJH110"}, {"disabled": false, "key": "dev-language", "value": "zh_cn"}, {"disabled": false, "key": "dev-region", "value": "cn"}, {"disabled": false, "key": "dev-timezone", "value": "Asia/Shanghai"}, {"disabled": false, "key": "dev-app-version", "value": "101016020"}, {"disabled": false, "key": "dev-channel", "value": "1001"}, {"disabled": false, "key": "dev-package", "value": "com.oppo.music"}, {"disabled": false, "key": "dev-id", "value": "d41d8cd98f00b204e9800998ecf8427e"}, {"disabled": false, "key": "dev-pid", "value": "6a7b1402abf04f05a2ddd6738854969b"}, {"disabled": false, "key": "dev-ab-experiment", "value": "DLrCpnXX_ij-V0O7BSB6wOBAXrSSvtuc5mXbw2MouedA-_q9fjFi38jUt_nbhwanYW1m0iG5iGaLyL58hVyH6gDoKCSWnXs2bzEvlAGenoJDERGW4AX8QHLZ8E_L4xR8b0TjlHcPzTyE8Wv-450M6Q=="}, {"disabled": false, "key": "dev-distinct-id", "value": "520d5520-73da-402b-9165-80dfcc9c737a"}, {"disabled": false, "key": "dev-android-release-version", "value": "9"}, {"disabled": false, "key": "dev-android-version", "value": "28"}, {"disabled": false, "key": "dev-net", "value": "WIFI"}, {"disabled": false, "key": "dev-ou-id", "value": ""}, {"disabled": false, "key": "dev-du-id", "value": ""}, {"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}], "method": "GET", "baseUrl": "https://api-server.allsaints.top", "body": {}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取请求接口的参数方法:https://apifox.com/help/pre-post-processors-and-scripts/scripts/api-references/pm-reference#pm-request", "// 获取请求接口的params参数，传入给python脚本调用", "var r_params = pm.request.url.query; ", "// 把数组格式的params数据转换为json字符串", "const result = r_params.reduce((obj, item) => {", "  obj[item.key] = item.value;", "  return obj;", "}, {});", "var params_str= JSON.stringify(result);", "// console.log(\"params参数:\",params_str);", "", "const res = await pm.executeAsync(\"./script/api/getSign.py\", [params_str], { command: 'python3' });", "// console.log(\"python脚本返回内容:\",res)", "// 因为python脚本返回的字符串有单引号，js转换json格式内容时需要将单引号替换为双引号", "var res1 = res.replace(/'/g, '\"');", "// /使用修正后的字符串进行 JSON 解析", "var api_params = JSON.parse(res1);", "console.log('脚本生成的api_params: ', api_params);", "// 第1种方法: 根据python脚本生成的sign内容，直接修改请求的params参数即可", "pm.request.url.query.upsert({ key: \"nonce\", value: api_params.nonce})", "pm.request.url.query.upsert({ key: \"ts\", value: api_params.ts})", "pm.request.url.query.upsert({ key: \"sign\", value: api_params.sign})", "console.log('请求时的api_params参数:',pm.request.url.getQueryString())", "", "// 第2种方法: 写入环境变量，此方案需要在接口里设置参数引用环境变量", "// pm.environment.set(\"api_params\", api_params);", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.code 等于 0000`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.code`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`0000`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.data 不为空 `);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "          ", "        if(value === null){", "          pm.expect(value).to.not.be.null", "        } else if(value === undefined) {", "          pm.expect(value).to.not.be.undefined", "        } else {", "          pm.expect(String(value)).to.not.be.empty", "        }", "      ", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}], "responseDefinition": {"id": 655499845, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"platform": {"type": "integer"}, "splash": {"type": "object", "properties": {"pos_id": {"type": "string"}, "max_count_per_day": {"type": "integer"}, "show_gap": {"type": "integer"}, "start_time": {"type": "integer"}, "end_time": {"type": "integer"}, "flag": {"type": "boolean"}, "max_load_time": {"type": "integer"}, "request_gap": {"type": "integer"}, "timeout": {"type": "integer"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}, "exVipProtectPeriod": {"type": "integer"}}, "required": ["show_gap", "flag", "status", "coverUrl"], "x-apifox-orders": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "max_load_time", "request_gap", "timeout", "coverUrl", "status", "exVipProtectPeriod"]}, "main_interstitial": {"type": "object", "properties": {"show_gap": {"type": "integer"}, "flag": {"type": "boolean"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}}, "required": ["show_gap", "flag", "coverUrl", "status"], "x-apifox-orders": ["show_gap", "flag", "coverUrl", "status"]}, "listen_interstitial": {"type": "object", "properties": {"show_gap": {"type": "integer"}, "flag": {"type": "boolean"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}}, "required": ["show_gap", "flag", "coverUrl", "status"], "x-apifox-orders": ["show_gap", "flag", "coverUrl", "status"]}, "mine_interstitial": {"type": "object", "properties": {"show_gap": {"type": "integer"}, "flag": {"type": "boolean"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}}, "required": ["show_gap", "flag", "coverUrl", "status"], "x-apifox-orders": ["show_gap", "flag", "coverUrl", "status"]}, "homeBanner": {"type": "object", "properties": {"show_gap": {"type": "integer"}, "flag": {"type": "boolean"}, "position": {"type": "string"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}}, "required": ["show_gap", "flag", "position", "coverUrl", "status"], "x-apifox-orders": ["show_gap", "flag", "position", "coverUrl", "status"]}, "personalizeRecommendation": {"type": "object", "properties": {"show_gap": {"type": "integer"}, "flag": {"type": "boolean"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}}, "required": ["show_gap", "flag", "coverUrl", "status"], "x-apifox-orders": ["show_gap", "flag", "coverUrl", "status"]}, "newSongColumn": {"type": "object", "properties": {"show_gap": {"type": "integer"}, "flag": {"type": "boolean"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}}, "required": ["show_gap", "flag", "coverUrl", "status"], "x-apifox-orders": ["show_gap", "flag", "coverUrl", "status"]}, "listDetailsPage": {"type": "object", "properties": {"pos_id": {"type": "string"}, "max_count_per_day": {"type": "integer"}, "show_gap": {"type": "integer"}, "start_time": {"type": "integer"}, "end_time": {"type": "integer"}, "flag": {"type": "boolean"}, "max_load_time": {"type": "integer"}, "request_gap": {"type": "integer"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}, "exVipProtectPeriod": {"type": "integer"}}, "required": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "max_load_time", "request_gap", "coverUrl", "status"], "x-apifox-orders": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "max_load_time", "request_gap", "coverUrl", "status", "exVipProtectPeriod"]}, "listeningDetailsPage": {"type": "object", "properties": {"show_gap": {"type": "integer"}, "flag": {"type": "boolean"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}}, "required": ["show_gap", "flag", "coverUrl", "status"], "x-apifox-orders": ["show_gap", "flag", "coverUrl", "status"]}, "userBanner": {"type": "object", "properties": {"show_gap": {"type": "integer"}, "flag": {"type": "boolean"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}}, "required": ["show_gap", "flag", "coverUrl", "status"], "x-apifox-orders": ["show_gap", "flag", "coverUrl", "status"]}, "inStationPush": {"type": "object", "properties": {"show_gap": {"type": "integer"}, "flag": {"type": "boolean"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}}, "required": ["show_gap", "flag", "coverUrl", "status"], "x-apifox-orders": ["show_gap", "flag", "coverUrl", "status"]}, "searchBanner": {"type": "object", "properties": {"pos_id": {"type": "string"}, "max_count_per_day": {"type": "integer"}, "show_gap": {"type": "integer"}, "start_time": {"type": "integer"}, "end_time": {"type": "integer"}, "flag": {"type": "boolean"}, "max_load_time": {"type": "integer"}, "request_gap": {"type": "integer"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}, "exVipProtectPeriod": {"type": "integer"}}, "required": ["show_gap", "flag", "coverUrl", "status"], "x-apifox-orders": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "max_load_time", "request_gap", "coverUrl", "status", "exVipProtectPeriod"]}, "listenMusicEarnings": {"type": "object", "properties": {"show_gap": {"type": "integer"}, "flag": {"type": "boolean"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}}, "required": ["show_gap", "flag", "coverUrl", "status"], "x-apifox-orders": ["show_gap", "flag", "coverUrl", "status"]}, "playPage": {"type": "object", "properties": {"pos_id": {"type": "string"}, "max_count_per_day": {"type": "integer"}, "show_gap": {"type": "integer"}, "start_time": {"type": "integer"}, "end_time": {"type": "integer"}, "flag": {"type": "boolean"}, "coverWaitDuration": {"type": "integer"}, "showDuration": {"type": "integer"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}, "exVipProtectPeriod": {"type": "integer"}}, "required": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "coverWaitDuration", "showDuration", "coverUrl", "status"], "x-apifox-orders": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "coverWaitDuration", "showDuration", "coverUrl", "status", "exVipProtectPeriod"]}, "circleRedIncentiveVideo": {"type": "object", "properties": {"pos_id": {"type": "string"}, "max_count_per_day": {"type": "integer"}, "show_gap": {"type": "integer"}, "start_time": {"type": "integer"}, "end_time": {"type": "integer"}, "flag": {"type": "boolean"}, "max_load_time": {"type": "integer"}, "request_gap": {"type": "integer"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}, "loading": {"type": "integer"}}, "required": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "max_load_time", "request_gap", "coverUrl", "status", "loading"], "x-apifox-orders": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "max_load_time", "request_gap", "coverUrl", "status", "loading"]}, "kingKongIncentiveVideo": {"type": "object", "properties": {"pos_id": {"type": "string"}, "max_count_per_day": {"type": "integer"}, "show_gap": {"type": "integer"}, "start_time": {"type": "integer"}, "end_time": {"type": "integer"}, "flag": {"type": "boolean"}, "max_load_time": {"type": "integer"}, "request_gap": {"type": "integer"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}}, "required": ["show_gap", "flag", "coverUrl", "status"], "x-apifox-orders": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "max_load_time", "request_gap", "coverUrl", "status"]}, "feedADs": {"type": "object", "properties": {"pos_id": {"type": "string"}, "max_count_per_day": {"type": "integer"}, "show_gap": {"type": "integer"}, "start_time": {"type": "integer"}, "end_time": {"type": "integer"}, "flag": {"type": "boolean"}, "max_load_time": {"type": "integer"}, "request_gap": {"type": "integer"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}}, "required": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "max_load_time", "request_gap", "coverUrl", "status"], "x-apifox-orders": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "max_load_time", "request_gap", "coverUrl", "status"]}, "welfareVideo": {"type": "object", "properties": {"pos_id": {"type": "string"}, "max_count_per_day": {"type": "integer"}, "show_gap": {"type": "integer"}, "start_time": {"type": "integer"}, "end_time": {"type": "integer"}, "flag": {"type": "boolean"}, "max_load_time": {"type": "integer"}, "request_gap": {"type": "integer"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}, "loading": {"type": "integer"}}, "required": ["show_gap", "flag", "coverUrl", "status"], "x-apifox-orders": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "max_load_time", "request_gap", "coverUrl", "status", "loading"]}, "videoDetailBanner": {"type": "object", "properties": {"pos_id": {"type": "string"}, "max_count_per_day": {"type": "integer"}, "show_gap": {"type": "integer"}, "start_time": {"type": "integer"}, "end_time": {"type": "integer"}, "flag": {"type": "boolean"}, "max_load_time": {"type": "integer"}, "request_gap": {"type": "integer"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}}, "required": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "max_load_time", "request_gap", "coverUrl", "status"], "x-apifox-orders": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "max_load_time", "request_gap", "coverUrl", "status"]}, "videoDetailNativeBig": {"type": "object", "properties": {"pos_id": {"type": "string"}, "max_count_per_day": {"type": "integer"}, "show_gap": {"type": "integer"}, "start_time": {"type": "integer"}, "end_time": {"type": "integer"}, "flag": {"type": "boolean"}, "max_load_time": {"type": "integer"}, "request_gap": {"type": "integer"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}}, "required": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "max_load_time", "request_gap", "coverUrl", "status"], "x-apifox-orders": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "max_load_time", "request_gap", "coverUrl", "status"]}, "videoDetailNativeSmall": {"type": "object", "properties": {"show_gap": {"type": "integer"}, "flag": {"type": "boolean"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}}, "required": ["show_gap", "flag", "coverUrl", "status"], "x-apifox-orders": ["show_gap", "flag", "coverUrl", "status"]}, "ads_bubble": {"type": "object", "properties": {"pos_id": {"type": "string"}, "max_count_per_day": {"type": "integer"}, "show_gap": {"type": "integer"}, "start_time": {"type": "integer"}, "end_time": {"type": "integer"}, "flag": {"type": "boolean"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}}, "required": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "coverUrl", "status"], "x-apifox-orders": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "coverUrl", "status"]}, "welfareFreeMembership": {"type": "object", "properties": {"pos_id": {"type": "string"}, "max_count_per_day": {"type": "integer"}, "show_gap": {"type": "integer"}, "start_time": {"type": "integer"}, "end_time": {"type": "integer"}, "flag": {"type": "boolean"}, "max_load_time": {"type": "integer"}, "request_gap": {"type": "integer"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "countdown": {"type": "integer"}, "status": {"type": "integer"}, "loading": {"type": "integer"}, "exVipProtectPeriod": {"type": "integer"}}, "required": ["show_gap", "flag", "coverUrl", "status"], "x-apifox-orders": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "max_load_time", "request_gap", "coverUrl", "countdown", "status", "loading", "exVipProtectPeriod"]}, "welfareH5Interactive": {"type": "object", "properties": {"show_gap": {"type": "integer"}, "flag": {"type": "boolean"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}}, "required": ["show_gap", "flag", "coverUrl", "status"], "x-apifox-orders": ["show_gap", "flag", "coverUrl", "status"]}, "welfareHomeFreeMembership": {"type": "object", "properties": {"pos_id": {"type": "string"}, "max_count_per_day": {"type": "integer"}, "show_gap": {"type": "integer"}, "start_time": {"type": "integer"}, "end_time": {"type": "integer"}, "flag": {"type": "boolean"}, "max_load_time": {"type": "integer"}, "request_gap": {"type": "integer"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "countdown": {"type": "integer"}, "status": {"type": "integer"}, "loading": {"type": "integer"}, "handleUrlList": {"type": "array", "items": {"type": "string"}}, "isShowVipSongs": {"type": "integer"}, "requestProtectPeriod": {"type": "integer"}, "countdownErrPeriod": {"type": "integer"}}, "required": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "max_load_time", "request_gap", "coverUrl", "countdown", "status", "loading", "handleUrlList", "isShowVipSongs", "requestProtectPeriod", "countdownErrPeriod"], "x-apifox-orders": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "max_load_time", "request_gap", "coverUrl", "countdown", "status", "loading", "handleUrlList", "isShowVipSongs", "requestProtectPeriod", "countdownErrPeriod"]}, "homeFeedAd": {"type": "object", "properties": {"pos_id": {"type": "string"}, "max_count_per_day": {"type": "integer"}, "show_gap": {"type": "integer"}, "start_time": {"type": "integer"}, "end_time": {"type": "integer"}, "flag": {"type": "boolean"}, "max_load_time": {"type": "integer"}, "request_gap": {"type": "integer"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}, "exVipProtectPeriod": {"type": "integer"}}, "required": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "max_load_time", "request_gap", "coverUrl", "status"], "x-apifox-orders": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "max_load_time", "request_gap", "coverUrl", "status", "exVipProtectPeriod"]}, "messageCenterFeedAd": {"type": "object", "properties": {"show_gap": {"type": "integer"}, "flag": {"type": "boolean"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}}, "required": ["show_gap", "flag", "coverUrl", "status"], "x-apifox-orders": ["show_gap", "flag", "coverUrl", "status"]}, "tabVideoWaterFall": {"type": "object", "properties": {"pos_id": {"type": "string"}, "max_count_per_day": {"type": "integer"}, "show_gap": {"type": "integer"}, "start_time": {"type": "integer"}, "end_time": {"type": "integer"}, "flag": {"type": "boolean"}, "max_load_time": {"type": "integer"}, "request_gap": {"type": "integer"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}, "showIntervalNum": {"type": "integer"}}, "required": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "max_load_time", "request_gap", "coverUrl", "status", "showIntervalNum"], "x-apifox-orders": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "max_load_time", "request_gap", "coverUrl", "status", "showIntervalNum"]}, "freeMemberDPOne": {"type": "object", "properties": {"show_gap": {"type": "integer"}, "flag": {"type": "boolean"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}}, "required": ["show_gap", "flag", "coverUrl", "status"], "x-apifox-orders": ["show_gap", "flag", "coverUrl", "status"]}, "freeMemberDPTwo": {"type": "object", "properties": {"show_gap": {"type": "integer"}, "flag": {"type": "boolean"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}}, "required": ["show_gap", "flag", "coverUrl", "status"], "x-apifox-orders": ["show_gap", "flag", "coverUrl", "status"]}, "freeMemberDPThree": {"type": "object", "properties": {"show_gap": {"type": "integer"}, "flag": {"type": "boolean"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}}, "required": ["show_gap", "flag", "coverUrl", "status"], "x-apifox-orders": ["show_gap", "flag", "coverUrl", "status"]}, "externalMotivatedVideo": {"type": "object", "properties": {"pos_id": {"type": "string"}, "max_count_per_day": {"type": "integer"}, "show_gap": {"type": "integer"}, "start_time": {"type": "integer"}, "end_time": {"type": "integer"}, "flag": {"type": "boolean"}, "max_load_time": {"type": "integer"}, "request_gap": {"type": "integer"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}, "loading": {"type": "integer"}}, "required": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "max_load_time", "request_gap", "coverUrl", "status", "loading"], "x-apifox-orders": ["pos_id", "max_count_per_day", "show_gap", "start_time", "end_time", "flag", "max_load_time", "request_gap", "coverUrl", "status", "loading"]}, "freeMemberPopFeeds": {"type": "object", "properties": {"show_gap": {"type": "integer"}, "flag": {"type": "boolean"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}, "popupType": {"type": "integer"}}, "required": ["show_gap", "flag", "coverUrl", "status", "popupType"], "x-apifox-orders": ["show_gap", "flag", "coverUrl", "status", "popupType"]}, "freeListenPush": {"type": "object", "properties": {"show_gap": {"type": "integer"}, "flag": {"type": "boolean"}, "coverUrl": {"type": "object", "properties": {}, "x-apifox-orders": []}, "status": {"type": "integer"}}, "required": ["show_gap", "flag", "coverUrl", "status"], "x-apifox-orders": ["show_gap", "flag", "coverUrl", "status"]}}, "required": ["platform", "main_interstitial", "listen_interstitial", "mine_interstitial", "homeBanner", "personalizeRecommendation", "newSongColumn", "listeningDetailsPage", "userBanner", "inStationPush", "searchBanner", "listenMusicEarnings", "playPage", "circleRedIncentiveVideo", "kingKongIncentiveVideo", "feedADs", "welfareVideo", "videoDetailBanner", "videoDetailNativeBig", "videoDetailNativeSmall", "ads_bubble", "welfareFreeMembership", "welfareH5Interactive", "welfareHomeFreeMembership", "homeFeedAd", "messageCenterFeedAd", "tabVideoWaterFall", "freeMemberDPOne", "freeMemberDPTwo", "freeMemberDPThree", "externalMotivatedVideo", "freeMemberPopFeeds", "freeListenPush", "listDetailsPage", "splash"], "x-apifox-orders": ["platform", "splash", "main_interstitial", "listen_interstitial", "mine_interstitial", "homeBanner", "personalizeRecommendation", "newSongColumn", "listDetailsPage", "listeningDetailsPage", "userBanner", "inStationPush", "searchBanner", "listenMusicEarnings", "playPage", "circleRedIncentiveVideo", "kingKongIncentiveVideo", "feedADs", "welfareVideo", "videoDetailBanner", "videoDetailNativeBig", "videoDetailNativeSmall", "ads_bubble", "welfareFreeMembership", "welfareH5Interactive", "welfareHomeFreeMembership", "homeFeedAd", "messageCenterFeedAd", "tabVideoWaterFall", "freeMemberDPOne", "freeMemberDPTwo", "freeMemberDPThree", "externalMotivatedVideo", "freeMemberPopFeeds", "freeListenPush"]}}, "required": ["code", "message", "data"], "x-apifox-orders": ["code", "message", "data"]}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "oasExtensions": "", "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "9bb5de07-36f5-409d-a84b-0d968637057d", "id": "d926dc91-b91c-57ce-a3b2-baf3bfe98119", "type": "http", "name": "app旧广告-获取配置信息成功", "projectId": 4800107, "relatedId": 6431181, "environmentId": 32208946, "blockNumber": 45, "httpApiId": 284367317, "httpApiCaseId": 255172117, "httpApiName": "旧广告-获取配置信息", "httpApiPath": "/api/v2/setting/advertisementConfig", "httpApiMethod": "get", "httpApiCaseName": "app旧广告-获取配置信息"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "1c7bcb6f-7924-5544-9c0e-34657bc995fe", "name": "首页-获取推荐栏目二级资源", "request": {"url": {"protocol": "https", "path": ["api", "v2", "index", "sectionListRS"], "host": ["api-server", "allsaints", "top"], "query": [{"disabled": false, "key": "isNew", "value": "0"}, {"disabled": false, "key": "tabId", "value": "43988"}, {"disabled": false, "key": "uid", "value": "o_id_eEnWgHTJC4nQdZHi7mAQ9Q"}, {"disabled": false, "key": "uid", "value": "o_id_eEnWgHTJC4nQdZHi7mAQ9Q"}, {"disabled": false, "key": "baseType", "value": "0"}, {"disabled": false, "key": "version", "value": "**********"}, {"disabled": false, "key": "buildVersion", "value": "***********.0test_341315b_250415"}, {"disabled": false, "key": "ak", "value": "test"}, {"disabled": false, "key": "nonce", "value": "afcf3245ff84419fa7e024d659ea952c"}, {"disabled": false, "key": "ts", "value": "1745722768148"}, {"disabled": false, "key": "utoken", "value": "41b03ddb3c9842849b1da22a04508b97"}, {"disabled": false, "key": "sign", "value": "4cee72515959d9df55859c9e361c88314c12a7f7"}], "variable": []}, "header": [{"disabled": false, "key": "sentry-trace", "value": "50e23a30a3de4895870223c818846b7e-e5d6c9358cbb4a4c"}, {"disabled": false, "key": "baggage", "value": "sentry-environment=test,sentry-public_key=be5e540abde2d5f0286c9a0fb535c419,sentry-release=***********.0test_341315b_250415,sentry-sample_rand=0.1452891739399573,sentry-trace_id=50e23a30a3de4895870223c818846b7e"}, {"disabled": false, "key": "dev-build-version", "value": "***********.0test_341315b_250415"}, {"disabled": false, "key": "dev-brand", "value": "OPPO"}, {"disabled": false, "key": "dev-manufacturer", "value": "OPPO"}, {"disabled": false, "key": "dev-model", "value": "PJH110"}, {"disabled": false, "key": "dev-language", "value": "zh_cn"}, {"disabled": false, "key": "dev-region", "value": "cn"}, {"disabled": false, "key": "dev-timezone", "value": "Asia/Shanghai"}, {"disabled": false, "key": "dev-app-version", "value": "101031410"}, {"disabled": false, "key": "dev-channel", "value": "1001"}, {"disabled": false, "key": "dev-package", "value": "com.oppo.music"}, {"disabled": false, "key": "dev-id", "value": "d41d8cd98f00b204e9800998ecf8427e"}, {"disabled": false, "key": "dev-pid", "value": "532c01203bb7439aa9b87e8524b2deaa"}, {"disabled": false, "key": "dev-ab-experiment", "value": "wRpt1_2EIQRVmwLXYaC9yscbT6pgcJglk3Is2OSDV4xKZwm5Z8sG8SY5F6VB0T4B4U93PHHyBi91SasYyFgvCkSaWxMburFraE0ecl-804fug_0-duBpGpGoGKeqSWkjZtIWblQk9Wf0O7NaYT4KtIYbmH-l1G0lY7NkaZibuWmqWm1qj6wj2XrvfWJ-TvS4"}, {"disabled": false, "key": "dev-distinct-id", "value": "692f4ccc-3d4b-4ce2-9165-e5fbff62a4af"}, {"disabled": false, "key": "dev-android-release-version", "value": "9"}, {"disabled": false, "key": "dev-android-version", "value": "28"}, {"disabled": false, "key": "dev-net", "value": "WIFI"}, {"disabled": false, "key": "dev-ou-id", "value": ""}, {"disabled": false, "key": "dev-du-id", "value": ""}, {"disabled": false, "key": "dev-mirror", "value": "1"}, {"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}], "method": "GET", "baseUrl": "https://api-server.allsaints.top", "body": {}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取请求接口的参数方法:https://apifox.com/help/pre-post-processors-and-scripts/scripts/api-references/pm-reference#pm-request", "// 获取请求接口的params参数，传入给python脚本调用", "var r_params = pm.request.url.query; ", "// 把数组格式的params数据转换为json字符串", "const result = r_params.reduce((obj, item) => {", "  obj[item.key] = item.value;", "  return obj;", "}, {});", "var params_str= JSON.stringify(result);", "// console.log(\"params参数:\",params_str);", "", "const res = await pm.executeAsync(\"./script/api/getSign.py\", [params_str], { command: 'python3' });", "// console.log(\"python脚本返回内容:\",res)", "// 因为python脚本返回的字符串有单引号，js转换json格式内容时需要将单引号替换为双引号", "var res1 = res.replace(/'/g, '\"');", "// /使用修正后的字符串进行 JSON 解析", "var api_params = JSON.parse(res1);", "console.log('脚本生成的api_params: ', api_params);", "// 第1种方法: 根据python脚本生成的sign内容，直接修改请求的params参数即可", "pm.request.url.query.upsert({ key: \"nonce\", value: api_params.nonce})", "pm.request.url.query.upsert({ key: \"ts\", value: api_params.ts})", "pm.request.url.query.upsert({ key: \"sign\", value: api_params.sign})", "console.log('请求时的api_params参数:',pm.request.url.getQueryString())", "", "// 第2种方法: 写入环境变量，此方案需要在接口里设置参数引用环境变量", "// pm.environment.set(\"api_params\", api_params);", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.code 等于 0000`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.code`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`0000`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.data[?(@.title==\"私人专属好歌\")].list 不为空 `);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data[?(@.title==\"私人专属好歌\")].list`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "          ", "        if(value === null){", "          pm.expect(value).to.not.be.null", "        } else if(value === undefined) {", "          pm.expect(value).to.not.be.undefined", "        } else {", "          pm.expect(String(value)).to.not.be.empty", "        }", "      ", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.2.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.data[?(@.title=='为你推荐今日歌单')].list 不为空 `);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data[?(@.title=='为你推荐今日歌单')].list`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "          ", "        if(value === null){", "          pm.expect(value).to.not.be.null", "        } else if(value === undefined) {", "          pm.expect(value).to.not.be.undefined", "        } else {", "          pm.expect(String(value)).to.not.be.empty", "        }", "      ", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.3.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.data[?(@.title=='私人专属歌单')].list 不为空 `);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data[?(@.title=='私人专属歌单')].list`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "          ", "        if(value === null){", "          pm.expect(value).to.not.be.null", "        } else if(value === undefined) {", "          pm.expect(value).to.not.be.undefined", "        } else {", "          pm.expect(String(value)).to.not.be.empty", "        }", "      ", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.4.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.data[?(@.title=='更多为你推荐')].list 不为空 `);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data[?(@.title=='更多为你推荐')].list`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "          ", "        if(value === null){", "          pm.expect(value).to.not.be.null", "        } else if(value === undefined) {", "          pm.expect(value).to.not.be.undefined", "        } else {", "          pm.expect(String(value)).to.not.be.empty", "        }", "      ", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}], "responseDefinition": {"id": 665112077, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "type": {"type": "integer"}, "title": {"type": "string"}, "handleUrl": {"type": "string"}, "recomDTO": {"type": "object", "properties": {"algorDTO": {"type": "object", "properties": {"algorCode": {"type": "string"}, "algorParam": {"type": "string"}}, "required": ["algorCode", "algor<PERSON><PERSON><PERSON>"], "x-apifox-orders": ["algorCode", "algor<PERSON><PERSON><PERSON>"]}, "abKey": {"type": "string"}}, "required": ["algorDTO", "a<PERSON><PERSON><PERSON>"], "x-apifox-orders": ["algorDTO", "a<PERSON><PERSON><PERSON>"]}, "list": {"type": ["array", "null"], "items": {"type": "object", "properties": {"id": {"type": ["string", "integer"]}, "name": {"type": "string"}, "cpId": {"type": "integer"}, "coverSmall": {"type": "string"}, "coverMiddle": {"type": "string"}, "coverLarge": {"type": "string"}, "actors": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}, "files": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "fileSize": {"type": "integer"}, "bitrateType": {"type": "integer"}, "format": {"type": "string"}, "duration": {"type": "integer"}, "dv": {"type": "integer"}, "ov": {"type": "integer"}, "index": {"type": "integer"}}, "required": ["id", "fileSize", "bitrateType", "format", "duration", "dv", "ov", "index"], "x-apifox-orders": ["id", "fileSize", "bitrateType", "format", "duration", "dv", "ov", "index"]}}, "album": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}, "lyricUrl": {"type": "string"}, "favorite": {"type": "integer"}, "playCount": {"type": "string"}, "setAsh": {"type": "integer"}, "mv": {"type": "object", "properties": {"id": {"type": "integer"}}, "required": ["id"], "x-apifox-orders": ["id"]}, "vip": {"type": "integer"}, "priceType": {"type": "integer"}, "pureAudio": {"type": "integer"}, "commentSwitchStatus": {"type": "integer"}, "ringtoneDpLink": {"type": "string"}, "creator": {"type": "object", "properties": {}, "x-apifox-orders": []}, "algo": {"type": "object", "properties": {"type": {"type": "string"}, "score": {"type": "integer"}, "requestId": {"type": "string"}, "experimentId": {"type": "string"}}, "required": ["type", "score"], "x-apifox-orders": ["type", "score", "requestId", "experimentId"]}, "releaseDate": {"type": "integer"}, "ringtoneDpLinkType": {"type": "integer"}, "dolby": {"type": "integer"}, "stereoIconStatus": {"type": "integer"}, "ra360IconStatus": {"type": "integer"}, "type": {"type": "integer"}, "introduction": {"type": "string"}, "favoriteCount": {"type": "string"}, "shareCount": {"type": "string"}, "creatorName": {"type": "string"}, "songCount": {"type": "integer"}, "source": {"type": "string"}, "recomStatus": {"type": "integer"}, "grid": {"type": "string"}, "creatorId": {"type": "string"}, "title": {"type": "string"}, "subTitle": {"type": "string"}, "songs": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "coverSmall": {"type": "string"}, "coverMiddle": {"type": "string"}, "coverLarge": {"type": "string"}, "actors": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}}, "required": ["id", "name", "coverSmall", "coverMiddle", "coverLarge", "actors"], "x-apifox-orders": ["id", "name", "coverSmall", "coverMiddle", "coverLarge", "actors"]}}, "handleUrl": {"type": "string"}, "reco": {"type": "string"}}, "x-apifox-orders": ["id", "name", "cpId", "coverSmall", "coverMiddle", "coverLarge", "actors", "files", "album", "lyricUrl", "favorite", "playCount", "<PERSON><PERSON><PERSON>", "mv", "vip", "priceType", "pureAudio", "commentSwitchStatus", "ringtoneDpLink", "creator", "algo", "releaseDate", "ringtoneDpLinkType", "dolby", "stereoIconStatus", "ra360IconStatus", "type", "introduction", "favoriteCount", "shareCount", "<PERSON><PERSON><PERSON>", "songCount", "source", "recomStatus", "grid", "creatorId", "title", "subTitle", "songs", "handleUrl", "reco"]}}, "recommended": {"type": "integer"}, "showPreferenceSetting": {"type": "integer"}, "showResNum": {"type": "integer"}, "columnName": {"type": "string"}, "pageResNum": {"type": "integer"}}, "required": ["type", "title", "handleUrl", "recomDTO", "list", "recommended", "showPreferenceSetting", "columnName", "id"], "x-apifox-orders": ["id", "type", "title", "handleUrl", "recomDTO", "list", "recommended", "showPreferenceSetting", "showResNum", "columnName", "pageResNum"]}}}, "required": ["code", "message", "data"], "x-apifox-orders": ["code", "message", "data"]}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "oasExtensions": "", "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "5e7fe05e-f73b-4d6a-92e5-81e777989dd4", "id": "1c7bcb6f-7924-5544-9c0e-34657bc995fe", "type": "http", "name": "首页-获取推荐栏目二级资源", "projectId": 4800107, "relatedId": 6362006, "environmentId": 32208946, "blockNumber": 8, "httpApiId": 289787054, "httpApiCaseId": 256463919, "httpApiName": "获取首页推荐栏目二级资源", "httpApiPath": "/api/v2/index/sectionListRS", "httpApiMethod": "get", "httpApiCaseName": "获取推荐栏目二级资源"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "b93390be-225b-50f5-904e-f3dde56f6f4b", "name": "首页-【推荐Tab】金刚区", "request": {"url": {"protocol": "https", "path": ["api", "v2", "index", "KingKongV2"], "host": ["api-server", "allsaints", "top"], "query": [{"disabled": false, "key": "isNew", "value": "1"}, {"disabled": false, "key": "version", "value": "**********"}, {"disabled": false, "key": "buildVersion", "value": "40.**********_6e744e9_250422"}, {"disabled": false, "key": "ak", "value": "test"}, {"disabled": false, "key": "nonce", "value": "935faf92604e498b8a57dbc11680837d"}, {"disabled": false, "key": "ts", "value": "1745462565833"}, {"disabled": false, "key": "sign", "value": "3406fe6c6d9e4a441fc6a777800abe45187d89f5"}], "variable": []}, "header": [{"disabled": false, "key": "dev-build-version", "value": "40.**********_6e744e9_250422"}, {"disabled": false, "key": "dev-brand", "value": "OPPO"}, {"disabled": false, "key": "dev-manufacturer", "value": "OPPO"}, {"disabled": false, "key": "dev-model", "value": "PCRT00"}, {"disabled": false, "key": "dev-language", "value": "zh_cn"}, {"disabled": false, "key": "dev-region", "value": "cn"}, {"disabled": false, "key": "dev-timezone", "value": "Asia/Shanghai"}, {"disabled": false, "key": "dev-app-version", "value": "101031411"}, {"disabled": false, "key": "dev-channel", "value": "1001"}, {"disabled": false, "key": "dev-package", "value": "com.heytap.music"}, {"disabled": false, "key": "dev-id", "value": "d41d8cd98f00b204e9800998ecf8427e"}, {"disabled": false, "key": "dev-pid", "value": "15681d7df8814ff491fa55ea0ba83971"}, {"disabled": false, "key": "dev-ab-experiment", "value": "CL1-D<PERSON>uQfil7NCJPH7ORjZETuInhb51gZyOB7B2FD2IzLP3Lb1F4W8db2ly0lQRVsaEJJ33MIfYflXe0nDjkgQdyc7uyVdlgRT_rfKYZsL8XpLQDg24f8xVpP2dk2CZvuUCeoxNY43CwO4urqgexyHGWwW-AS9HVAlq1fH7A8pm8l7IG42BV8FCvnUpcKYhkLBT1-QKI3zPAarJ4tYqzRyfaqalZH-Y99ucEEPLXpybE3qRHxACTgmk6AAWlYkwT"}, {"disabled": false, "key": "dev-distinct-id", "value": "9f2085fd-c089-4ebe-b26a-0e993bcfdd04"}, {"disabled": false, "key": "dev-android-release-version", "value": "9"}, {"disabled": false, "key": "dev-android-version", "value": "28"}, {"disabled": false, "key": "dev-net", "value": "WIFI"}, {"disabled": false, "key": "dev-ou-id", "value": ""}, {"disabled": false, "key": "dev-du-id", "value": ""}, {"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}], "method": "GET", "baseUrl": "https://api-server.allsaints.top", "body": {}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.code 等于 0000`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.code`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`0000`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.data[0].label 等于 每日推荐`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data[0].label`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`每日推荐`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}], "responseDefinition": {"id": 662888424, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "coverSmall": {"type": "string"}, "coverMiddle": {"type": "string"}, "coverLarge": {"type": "string"}, "type": {"type": "integer"}, "label": {"type": "string"}, "introduction": {"type": "string"}, "handleUrl": {"type": "string"}, "dailyRecommend": {"type": "integer"}, "payload": {"type": "object", "properties": {"songId": {"type": "integer"}, "algo": {"type": "object", "properties": {"type": {"type": "string"}, "score": {"type": "integer"}}, "required": ["type", "score"], "x-apifox-orders": ["type", "score"]}}, "required": ["songId", "algo"], "x-apifox-orders": ["songId", "algo"]}, "parentId": {"type": "integer"}, "topText": {"type": "string"}, "bottomText": {"type": "string"}}, "required": ["id", "coverSmall", "coverMiddle", "coverLarge", "type", "label", "introduction", "handleUrl", "dailyRecommend", "parentId", "topText", "bottomText"], "x-apifox-orders": ["id", "coverSmall", "coverMiddle", "coverLarge", "type", "label", "introduction", "handleUrl", "dailyRecommend", "payload", "parentId", "topText", "bottomText"]}}}, "required": ["code", "message"], "x-apifox-orders": ["code", "message", "data"]}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "oasExtensions": "", "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "5775cdcc-a48b-48f6-aaf4-bdedb7263da4", "id": "b93390be-225b-50f5-904e-f3dde56f6f4b", "type": "http", "name": "首页-【推荐Tab】金刚区", "projectId": 4800107, "relatedId": 6362006, "environmentId": 32208946, "blockNumber": 6, "httpApiId": 288488169, "httpApiCaseId": 255340076, "httpApiName": "首页-【推荐Tab】金刚区", "httpApiPath": "/api/v2/index/KingKongV2", "httpApiMethod": "get", "httpApiCaseName": "首页-【推荐Tab】金刚区"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "629bafdb-d8f7-5f9f-be8f-d96946715f8f", "name": "获取首页一级栏目列表", "request": {"url": {"protocol": "https", "path": ["api", "v2", "index", "getTabs"], "host": ["api-server", "allsaints", "top"], "query": [{"disabled": false, "key": "isMinor", "value": "0"}, {"disabled": false, "key": "uid", "value": ""}, {"disabled": false, "key": "version", "value": "**********"}, {"disabled": false, "key": "buildVersion", "value": "***********.0test_341315b_250415"}, {"disabled": false, "key": "ak", "value": "test"}, {"disabled": false, "key": "nonce", "value": "86bc80e4d49840b8b49b57fede110b33"}, {"disabled": false, "key": "ts", "value": "1744784330877"}, {"disabled": false, "key": "sign", "value": "13dfadacc309b9d50f24d65488147f259361d28a"}], "variable": []}, "header": [{"disabled": false, "key": "sentry-trace", "value": "dac775ce6c5f434eb6a15f2fe9dc3e41-e71b3f4916884f17-1"}, {"disabled": false, "key": "baggage", "value": "sentry-environment=test,sentry-public_key=be5e540abde2d5f0286c9a0fb535c419,sentry-release=***********.0test_341315b_250415,sentry-sample_rand=0.8432396181910827,sentry-sample_rate=1,sentry-sampled=true,sentry-trace_id=dac775ce6c5f434eb6a15f2fe9dc3e41,sentry-transaction=MainActivity"}, {"disabled": false, "key": "dev-build-version", "value": "***********.0test_341315b_250415"}, {"disabled": false, "key": "dev-brand", "value": "OPPO"}, {"disabled": false, "key": "dev-manufacturer", "value": "OPPO"}, {"disabled": false, "key": "dev-model", "value": "PJH110"}, {"disabled": false, "key": "dev-language", "value": "zh_cn"}, {"disabled": false, "key": "dev-region", "value": "cn"}, {"disabled": false, "key": "dev-timezone", "value": "Asia/Shanghai"}, {"disabled": false, "key": "dev-app-version", "value": "101031410"}, {"disabled": false, "key": "dev-channel", "value": "1001"}, {"disabled": false, "key": "dev-package", "value": "com.oppo.music"}, {"disabled": false, "key": "dev-id", "value": "d41d8cd98f00b204e9800998ecf8427e"}, {"disabled": false, "key": "dev-pid", "value": "95ac83a0c2c24e45a501b1678afcbeef"}, {"disabled": false, "key": "dev-ab-experiment", "value": "DLrCpnXX_ij-V0O7BSB6wA6Yd0ILqw_eZikYZxFoiylLVlW4lpnzW5KgbF5pfq0j4A54kylkS03lYQRu9oPzwyPK9cA52ytpD4NDEh2GRmstMwJIVbIQjc2eq01cU5MBraOiNnKicJ8Zb1nFnVr0GXp6UXBMWe0gNcCCN-R28tRL2xrN4LeGRMoY3w6rmQyMaJu6wwuKovDDOXzq01gKEJLCmHvXei7L_cEvk0D_cpw="}, {"disabled": false, "key": "dev-distinct-id", "value": "133af0af-7a3d-4d75-96a1-35287cd9a0f7"}, {"disabled": false, "key": "dev-android-release-version", "value": "9"}, {"disabled": false, "key": "dev-android-version", "value": "28"}, {"disabled": false, "key": "dev-net", "value": "WIFI"}, {"disabled": false, "key": "dev-ou-id", "value": ""}, {"disabled": false, "key": "dev-du-id", "value": ""}, {"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}], "method": "GET", "baseUrl": "https://api-server.allsaints.top", "body": {}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取请求接口的参数方法:https://apifox.com/help/pre-post-processors-and-scripts/scripts/api-references/pm-reference#pm-request", "// 获取请求接口的params参数，传入给python脚本调用", "var r_params = pm.request.url.query; ", "// 把数组格式的params数据转换为json字符串", "const result = r_params.reduce((obj, item) => {", "  obj[item.key] = item.value;", "  return obj;", "}, {});", "var params_str= JSON.stringify(result);", "// console.log(\"params参数:\",params_str);", "", "const res = await pm.executeAsync(\"./script/api/getSign.py\", [params_str], { command: 'python3' });", "// console.log(\"python脚本返回内容:\",res)", "// 因为python脚本返回的字符串有单引号，js转换json格式内容时需要将单引号替换为双引号", "var res1 = res.replace(/'/g, '\"');", "// /使用修正后的字符串进行 JSON 解析", "var api_params = JSON.parse(res1);", "console.log('脚本生成的api_params: ', api_params);", "// 第1种方法: 根据python脚本生成的sign内容，直接修改请求的params参数即可", "pm.request.url.query.upsert({ key: \"nonce\", value: api_params.nonce})", "pm.request.url.query.upsert({ key: \"ts\", value: api_params.ts})", "pm.request.url.query.upsert({ key: \"sign\", value: api_params.sign})", "console.log('请求时的api_params参数:',pm.request.url.getQueryString())", "", "// 第2种方法: 写入环境变量，此方案需要在接口里设置参数引用环境变量", "// pm.environment.set(\"api_params\", api_params);", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.code 等于 0000`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.code`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`0000`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.data 不为空 `);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "          ", "        if(value === null){", "          pm.expect(value).to.not.be.null", "        } else if(value === undefined) {", "          pm.expect(value).to.not.be.undefined", "        } else {", "          pm.expect(String(value)).to.not.be.empty", "        }", "      ", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}], "responseDefinition": {"id": 656319901, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "tabType": {"type": "string"}, "tabContentType": {"type": "string"}, "cover": {"type": "string"}, "gifUrl": {"type": "string"}, "h5Url": {"type": "string"}, "handleUrl": {"type": "string"}, "styleId": {"type": "integer"}, "algo": {"type": "object", "properties": {"type": {"type": "string"}, "score": {"type": "integer"}}, "required": ["type", "score"]}, "algoCode": {"type": "integer"}}, "required": ["id", "name", "tabType", "tabContentType", "cover", "gifUrl", "h5Url", "handleUrl", "styleId", "algo", "algoCode"]}}}, "required": ["code", "message", "data"]}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "oasExtensions": "", "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "7d7a7652-1cca-47e4-a495-b43208398929", "id": "629bafdb-d8f7-5f9f-be8f-d96946715f8f", "type": "http", "name": "获取首页一级栏目列表", "projectId": 4800107, "relatedId": 6362008, "environmentId": 32208946, "blockNumber": 6, "httpApiId": 284853608, "httpApiCaseId": 252491349, "httpApiName": "获取首页一级栏目", "httpApiPath": "/api/v2/index/getTabs", "httpApiMethod": "get", "httpApiCaseName": "app获取首页一级栏目"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "848f8fc9-d72b-5901-bfd3-369f9b0c24d4", "name": "获取首页推荐栏目二级列表", "request": {"url": {"protocol": "https", "path": ["api", "v2", "index", "sectionListRS"], "host": ["api-server", "allsaints", "top"], "query": [{"disabled": false, "key": "isNew", "value": "0"}, {"disabled": false, "key": "tabId", "value": "43988"}, {"disabled": false, "key": "uid", "value": "o_id_eEnWgHTJC4nQdZHi7mAQ9Q"}, {"disabled": false, "key": "uid", "value": "o_id_eEnWgHTJC4nQdZHi7mAQ9Q"}, {"disabled": false, "key": "baseType", "value": "0"}, {"disabled": false, "key": "version", "value": "**********"}, {"disabled": false, "key": "buildVersion", "value": "***********.0test_341315b_250415"}, {"disabled": false, "key": "ak", "value": "test"}, {"disabled": false, "key": "nonce", "value": "afcf3245ff84419fa7e024d659ea952c"}, {"disabled": false, "key": "ts", "value": "1745722768148"}, {"disabled": false, "key": "utoken", "value": "41b03ddb3c9842849b1da22a04508b97"}, {"disabled": false, "key": "sign", "value": "4cee72515959d9df55859c9e361c88314c12a7f7"}], "variable": []}, "header": [{"disabled": false, "key": "sentry-trace", "value": "50e23a30a3de4895870223c818846b7e-e5d6c9358cbb4a4c"}, {"disabled": false, "key": "baggage", "value": "sentry-environment=test,sentry-public_key=be5e540abde2d5f0286c9a0fb535c419,sentry-release=***********.0test_341315b_250415,sentry-sample_rand=0.1452891739399573,sentry-trace_id=50e23a30a3de4895870223c818846b7e"}, {"disabled": false, "key": "dev-build-version", "value": "***********.0test_341315b_250415"}, {"disabled": false, "key": "dev-brand", "value": "OPPO"}, {"disabled": false, "key": "dev-manufacturer", "value": "OPPO"}, {"disabled": false, "key": "dev-model", "value": "PJH110"}, {"disabled": false, "key": "dev-language", "value": "zh_cn"}, {"disabled": false, "key": "dev-region", "value": "cn"}, {"disabled": false, "key": "dev-timezone", "value": "Asia/Shanghai"}, {"disabled": false, "key": "dev-app-version", "value": "101031410"}, {"disabled": false, "key": "dev-channel", "value": "1001"}, {"disabled": false, "key": "dev-package", "value": "com.oppo.music"}, {"disabled": false, "key": "dev-id", "value": "d41d8cd98f00b204e9800998ecf8427e"}, {"disabled": false, "key": "dev-pid", "value": "532c01203bb7439aa9b87e8524b2deaa"}, {"disabled": false, "key": "dev-ab-experiment", "value": "wRpt1_2EIQRVmwLXYaC9yscbT6pgcJglk3Is2OSDV4xKZwm5Z8sG8SY5F6VB0T4B4U93PHHyBi91SasYyFgvCkSaWxMburFraE0ecl-804fug_0-duBpGpGoGKeqSWkjZtIWblQk9Wf0O7NaYT4KtIYbmH-l1G0lY7NkaZibuWmqWm1qj6wj2XrvfWJ-TvS4"}, {"disabled": false, "key": "dev-distinct-id", "value": "692f4ccc-3d4b-4ce2-9165-e5fbff62a4af"}, {"disabled": false, "key": "dev-android-release-version", "value": "9"}, {"disabled": false, "key": "dev-android-version", "value": "28"}, {"disabled": false, "key": "dev-net", "value": "WIFI"}, {"disabled": false, "key": "dev-ou-id", "value": ""}, {"disabled": false, "key": "dev-du-id", "value": ""}, {"disabled": false, "key": "de", "value": ""}, {"disabled": false, "key": "dev-mirror", "value": "1"}, {"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}], "method": "GET", "baseUrl": "https://api-server.allsaints.top", "body": {}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.code 等于 0000`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.code`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`0000`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.data 不为空 `);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "          ", "        if(value === null){", "          pm.expect(value).to.not.be.null", "        } else if(value === undefined) {", "          pm.expect(value).to.not.be.undefined", "        } else {", "          pm.expect(String(value)).to.not.be.empty", "        }", "      ", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}], "responseDefinition": {"id": 665112077, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "type": {"type": "integer"}, "title": {"type": "string"}, "handleUrl": {"type": "string"}, "recomDTO": {"type": "object", "properties": {"algorDTO": {"type": "object", "properties": {"algorCode": {"type": "string"}, "algorParam": {"type": "string"}}, "required": ["algorCode", "algor<PERSON><PERSON><PERSON>"], "x-apifox-orders": ["algorCode", "algor<PERSON><PERSON><PERSON>"]}, "abKey": {"type": "string"}}, "required": ["algorDTO", "a<PERSON><PERSON><PERSON>"], "x-apifox-orders": ["algorDTO", "a<PERSON><PERSON><PERSON>"]}, "list": {"type": ["array", "null"], "items": {"type": "object", "properties": {"id": {"type": ["string", "integer"]}, "name": {"type": "string"}, "cpId": {"type": "integer"}, "coverSmall": {"type": "string"}, "coverMiddle": {"type": "string"}, "coverLarge": {"type": "string"}, "actors": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}, "files": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "fileSize": {"type": "integer"}, "bitrateType": {"type": "integer"}, "format": {"type": "string"}, "duration": {"type": "integer"}, "dv": {"type": "integer"}, "ov": {"type": "integer"}, "index": {"type": "integer"}}, "required": ["id", "fileSize", "bitrateType", "format", "duration", "dv", "ov", "index"], "x-apifox-orders": ["id", "fileSize", "bitrateType", "format", "duration", "dv", "ov", "index"]}}, "album": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}, "lyricUrl": {"type": "string"}, "favorite": {"type": "integer"}, "playCount": {"type": "string"}, "setAsh": {"type": "integer"}, "mv": {"type": "object", "properties": {"id": {"type": "integer"}}, "required": ["id"], "x-apifox-orders": ["id"]}, "vip": {"type": "integer"}, "priceType": {"type": "integer"}, "pureAudio": {"type": "integer"}, "commentSwitchStatus": {"type": "integer"}, "ringtoneDpLink": {"type": "string"}, "creator": {"type": "object", "properties": {}, "x-apifox-orders": []}, "algo": {"type": "object", "properties": {"type": {"type": "string"}, "score": {"type": "integer"}, "requestId": {"type": "string"}, "experimentId": {"type": "string"}}, "required": ["type", "score"], "x-apifox-orders": ["type", "score", "requestId", "experimentId"]}, "releaseDate": {"type": "integer"}, "ringtoneDpLinkType": {"type": "integer"}, "dolby": {"type": "integer"}, "stereoIconStatus": {"type": "integer"}, "ra360IconStatus": {"type": "integer"}, "type": {"type": "integer"}, "introduction": {"type": "string"}, "favoriteCount": {"type": "string"}, "shareCount": {"type": "string"}, "creatorName": {"type": "string"}, "songCount": {"type": "integer"}, "source": {"type": "string"}, "recomStatus": {"type": "integer"}, "grid": {"type": "string"}, "creatorId": {"type": "string"}, "title": {"type": "string"}, "subTitle": {"type": "string"}, "songs": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "coverSmall": {"type": "string"}, "coverMiddle": {"type": "string"}, "coverLarge": {"type": "string"}, "actors": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}}, "required": ["id", "name", "coverSmall", "coverMiddle", "coverLarge", "actors"], "x-apifox-orders": ["id", "name", "coverSmall", "coverMiddle", "coverLarge", "actors"]}}, "handleUrl": {"type": "string"}, "reco": {"type": "string"}}, "x-apifox-orders": ["id", "name", "cpId", "coverSmall", "coverMiddle", "coverLarge", "actors", "files", "album", "lyricUrl", "favorite", "playCount", "<PERSON><PERSON><PERSON>", "mv", "vip", "priceType", "pureAudio", "commentSwitchStatus", "ringtoneDpLink", "creator", "algo", "releaseDate", "ringtoneDpLinkType", "dolby", "stereoIconStatus", "ra360IconStatus", "type", "introduction", "favoriteCount", "shareCount", "<PERSON><PERSON><PERSON>", "songCount", "source", "recomStatus", "grid", "creatorId", "title", "subTitle", "songs", "handleUrl", "reco"]}}, "recommended": {"type": "integer"}, "showPreferenceSetting": {"type": "integer"}, "showResNum": {"type": "integer"}, "columnName": {"type": "string"}, "pageResNum": {"type": "integer"}}, "required": ["type", "title", "handleUrl", "recomDTO", "list", "recommended", "showPreferenceSetting", "columnName", "id"], "x-apifox-orders": ["id", "type", "title", "handleUrl", "recomDTO", "list", "recommended", "showPreferenceSetting", "showResNum", "columnName", "pageResNum"]}}}, "required": ["code", "message", "data"], "x-apifox-orders": ["code", "message", "data"]}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "oasExtensions": "", "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "6f3c5fb3-0f34-47ff-876c-7d435d10bb88", "id": "848f8fc9-d72b-5901-bfd3-369f9b0c24d4", "type": "http", "name": "获取首页推荐栏目二级列表", "projectId": 4800107, "relatedId": 6362008, "environmentId": 32208946, "blockNumber": 9, "httpApiId": 289787054, "httpApiCaseId": 265865249, "httpApiName": "获取首页推荐栏目二级资源", "httpApiPath": "/api/v2/index/sectionListRS", "httpApiMethod": "get", "httpApiCaseName": "获取首页推荐栏目二级资源"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "b41631b5-77d7-538e-833a-0754db644c78", "name": "app非爱听歌曲播放返回", "request": {"url": {"protocol": "https", "path": ["api", "v2", "song", "authPlay"], "host": ["api-server", "allsaints", "top"], "query": [{"disabled": false, "key": "songId", "value": "78179"}, {"disabled": false, "key": "fileId", "value": "1"}, {"disabled": false, "key": "bitRateType", "value": "0"}, {"disabled": false, "key": "musicId", "value": ""}, {"disabled": false, "key": "encryptedOaid", "value": ""}, {"disabled": false, "key": "aiAccompanimentSwitch", "value": "0"}, {"disabled": false, "key": "actionType", "value": "0"}, {"disabled": false, "key": "columnType", "value": "0"}, {"disabled": false, "key": "version", "value": "**********"}, {"disabled": false, "key": "buildVersion", "value": "40.**********_6e744e9_250422"}, {"disabled": false, "key": "ak", "value": "test"}, {"disabled": false, "key": "nonce", "value": "7bf151a3f7bc4c779937f416e7bd1be1"}, {"disabled": false, "key": "ts", "value": "1745474573181"}, {"disabled": false, "key": "sign", "value": "bb8abb985585b0d0eee26011f46ecd834f56f862"}], "variable": []}, "header": [{"disabled": false, "key": "dev-build-version", "value": "40.**********_6e744e9_250422"}, {"disabled": false, "key": "dev-brand", "value": "OPPO"}, {"disabled": false, "key": "dev-manufacturer", "value": "OPPO"}, {"disabled": false, "key": "dev-model", "value": "PCRT00"}, {"disabled": false, "key": "dev-language", "value": "zh_cn"}, {"disabled": false, "key": "dev-region", "value": "cn"}, {"disabled": false, "key": "dev-timezone", "value": "Asia/Shanghai"}, {"disabled": false, "key": "dev-app-version", "value": "101031411"}, {"disabled": false, "key": "dev-channel", "value": "1001"}, {"disabled": false, "key": "dev-package", "value": "com.heytap.music"}, {"disabled": false, "key": "dev-id", "value": "d41d8cd98f00b204e9800998ecf8427e"}, {"disabled": false, "key": "dev-pid", "value": "de9a9a8f8d9a459ba954a8a443e653eb"}, {"disabled": false, "key": "dev-ab-experiment", "value": ""}, {"disabled": false, "key": "dev-distinct-id", "value": "ba40e319-46d6-4dde-8e93-66385a2806fd"}, {"disabled": false, "key": "dev-android-release-version", "value": "9"}, {"disabled": false, "key": "dev-android-version", "value": "28"}, {"disabled": false, "key": "dev-net", "value": "WIFI"}, {"disabled": false, "key": "dev-ou-id", "value": ""}, {"disabled": false, "key": "dev-du-id", "value": ""}, {"disabled": false, "key": "failover", "value": "1"}, {"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}], "method": "GET", "baseUrl": "https://api-server.allsaints.top", "body": {}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取请求接口的参数方法:https://apifox.com/help/pre-post-processors-and-scripts/scripts/api-references/pm-reference#pm-request", "// 获取请求接口的params参数，传入给python脚本调用", "var r_params = pm.request.url.query; ", "// 把数组格式的params数据转换为json字符串", "const result = r_params.reduce((obj, item) => {", "  obj[item.key] = item.value;", "  return obj;", "}, {});", "var params_str= JSON.stringify(result);", "// console.log(\"params参数:\",params_str);", "", "const res = await pm.executeAsync(\"./script/api/getSign.py\", [params_str], { command: 'python3' });", "// console.log(\"python脚本返回内容:\",res)", "// 因为python脚本返回的字符串有单引号，js转换json格式内容时需要将单引号替换为双引号", "var res1 = res.replace(/'/g, '\"');", "// /使用修正后的字符串进行 JSON 解析", "var api_params = JSON.parse(res1);", "console.log('脚本生成的api_params: ', api_params);", "// 第1种方法: 根据python脚本生成的sign内容，直接修改请求的params参数即可", "pm.request.url.query.upsert({ key: \"nonce\", value: api_params.nonce})", "pm.request.url.query.upsert({ key: \"ts\", value: api_params.ts})", "pm.request.url.query.upsert({ key: \"sign\", value: api_params.sign})", "console.log('请求时的api_params参数:',pm.request.url.getQueryString())", "", "// 第2种方法: 写入环境变量，此方案需要在接口里设置参数引用环境变量", "// pm.environment.set(\"api_params\", api_params);", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.code 等于 0000`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.code`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`0000`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.data.url 不为空 `);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data.url`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "          ", "        if(value === null){", "          pm.expect(value).to.not.be.null", "        } else if(value === undefined) {", "          pm.expect(value).to.not.be.undefined", "        } else {", "          pm.expect(String(value)).to.not.be.empty", "        }", "      ", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}], "responseDefinition": {"id": 663238166, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}}, "required": ["code", "message"]}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "oasExtensions": "", "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "91af49e4-03b7-47dd-b023-18ff2fb46fd5", "id": "b41631b5-77d7-538e-833a-0754db644c78", "type": "http", "name": "app非爱听歌曲播放返回", "projectId": 4800107, "relatedId": 6362010, "environmentId": 32208946, "blockNumber": 4, "httpApiId": 288712936, "httpApiCaseId": 255552441, "httpApiName": "歌曲播放-灾备环境", "httpApiPath": "/api/v2/song/authPlay", "httpApiMethod": "get", "httpApiCaseName": "歌曲播放-灾备环境"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "a798d733-fc64-5c60-88ae-963f1ec72062", "name": "app爱听歌曲播放返回", "request": {"url": {"protocol": "https", "path": ["api", "v2", "song", "authPlay"], "host": ["api-server", "allsaints", "top"], "query": [{"disabled": false, "key": "songId", "value": "48740098"}, {"disabled": false, "key": "fileId", "value": "1"}, {"disabled": false, "key": "bitRateType", "value": "0"}, {"disabled": false, "key": "musicId", "value": ""}, {"disabled": false, "key": "encryptedOaid", "value": ""}, {"disabled": false, "key": "aiAccompanimentSwitch", "value": "0"}, {"disabled": false, "key": "actionType", "value": "0"}, {"disabled": false, "key": "columnType", "value": "0"}, {"disabled": false, "key": "version", "value": "**********"}, {"disabled": false, "key": "buildVersion", "value": "40.**********_6e744e9_250422"}, {"disabled": false, "key": "ak", "value": "test"}, {"disabled": false, "key": "nonce", "value": "7bf151a3f7bc4c779937f416e7bd1be1"}, {"disabled": false, "key": "ts", "value": "1745474573181"}, {"disabled": false, "key": "sign", "value": "bb8abb985585b0d0eee26011f46ecd834f56f862"}], "variable": []}, "header": [{"disabled": false, "key": "dev-build-version", "value": "40.**********_6e744e9_250422"}, {"disabled": false, "key": "dev-brand", "value": "OPPO"}, {"disabled": false, "key": "dev-manufacturer", "value": "OPPO"}, {"disabled": false, "key": "dev-model", "value": "PCRT00"}, {"disabled": false, "key": "dev-language", "value": "zh_cn"}, {"disabled": false, "key": "dev-region", "value": "cn"}, {"disabled": false, "key": "dev-timezone", "value": "Asia/Shanghai"}, {"disabled": false, "key": "dev-app-version", "value": "101031411"}, {"disabled": false, "key": "dev-channel", "value": "1001"}, {"disabled": false, "key": "dev-package", "value": "com.heytap.music"}, {"disabled": false, "key": "dev-id", "value": "d41d8cd98f00b204e9800998ecf8427e"}, {"disabled": false, "key": "dev-pid", "value": "de9a9a8f8d9a459ba954a8a443e653eb"}, {"disabled": false, "key": "dev-ab-experiment", "value": ""}, {"disabled": false, "key": "dev-distinct-id", "value": "ba40e319-46d6-4dde-8e93-66385a2806fd"}, {"disabled": false, "key": "dev-android-release-version", "value": "9"}, {"disabled": false, "key": "dev-android-version", "value": "28"}, {"disabled": false, "key": "dev-net", "value": "WIFI"}, {"disabled": false, "key": "dev-ou-id", "value": ""}, {"disabled": false, "key": "dev-du-id", "value": ""}, {"disabled": false, "key": "failover", "value": "1"}, {"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}], "method": "GET", "baseUrl": "https://api-server.allsaints.top", "body": {}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取请求接口的参数方法:https://apifox.com/help/pre-post-processors-and-scripts/scripts/api-references/pm-reference#pm-request", "// 获取请求接口的params参数，传入给python脚本调用", "var r_params = pm.request.url.query; ", "// 把数组格式的params数据转换为json字符串", "const result = r_params.reduce((obj, item) => {", "  obj[item.key] = item.value;", "  return obj;", "}, {});", "var params_str= JSON.stringify(result);", "// console.log(\"params参数:\",params_str);", "", "const res = await pm.executeAsync(\"./script/api/getSign.py\", [params_str], { command: 'python3' });", "// console.log(\"python脚本返回内容:\",res)", "// 因为python脚本返回的字符串有单引号，js转换json格式内容时需要将单引号替换为双引号", "var res1 = res.replace(/'/g, '\"');", "// /使用修正后的字符串进行 JSON 解析", "var api_params = JSON.parse(res1);", "console.log('脚本生成的api_params: ', api_params);", "// 第1种方法: 根据python脚本生成的sign内容，直接修改请求的params参数即可", "pm.request.url.query.upsert({ key: \"nonce\", value: api_params.nonce})", "pm.request.url.query.upsert({ key: \"ts\", value: api_params.ts})", "pm.request.url.query.upsert({ key: \"sign\", value: api_params.sign})", "console.log('请求时的api_params参数:',pm.request.url.getQueryString())", "", "// 第2种方法: 写入环境变量，此方案需要在接口里设置参数引用环境变量", "// pm.environment.set(\"api_params\", api_params);", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.code 等于 5001`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.code`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`5001`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}], "responseDefinition": {"id": 663238166, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}}, "required": ["code", "message"]}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "oasExtensions": "", "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "08b1a1d1-82b9-43e7-b650-5467cb7368fe", "id": "a798d733-fc64-5c60-88ae-963f1ec72062", "type": "http", "name": "app爱听歌曲播放返回", "projectId": 4800107, "relatedId": 6362010, "environmentId": 32208946, "blockNumber": 5, "httpApiId": 288712936, "httpApiCaseId": 255553374, "httpApiName": "歌曲播放-灾备环境", "httpApiPath": "/api/v2/song/authPlay", "httpApiMethod": "get", "httpApiCaseName": "歌曲播放-灾备环境"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "a474b9b3-5ad5-58b2-bbc1-5ab16545a18a", "name": "搜索框搜索综合内容", "request": {"url": {"protocol": "https", "path": ["api", "v2", "search", "synthetical"], "host": ["api-server", "allsaints", "top"], "query": [{"disabled": false, "key": "keyword", "value": "沦陷"}, {"disabled": false, "key": "originalSearch", "value": "0"}, {"disabled": false, "key": "isMinor", "value": "0"}, {"disabled": false, "key": "version", "value": "10.16.70"}, {"disabled": false, "key": "buildVersion", "value": "40.10.16.70test_6f652d0_241106"}, {"disabled": false, "key": "ak", "value": "test"}, {"disabled": false, "key": "nonce", "value": "ba6814a0a3c84e45965d4b34f4c40c9f"}, {"disabled": false, "key": "ts", "value": "1731050489591"}, {"disabled": false, "key": "sign", "value": "57f90796586f52fe3c77e5cca76be9016cb017f5"}], "variable": []}, "header": [{"disabled": false, "key": "dev-build-version", "value": " 40.10.16.70test_6f652d0_241106"}, {"disabled": false, "key": "dev-brand", "value": " OPPO"}, {"disabled": false, "key": "dev-manufacturer", "value": " OPPO"}, {"disabled": false, "key": "dev-model", "value": " PDNM00"}, {"disabled": false, "key": "dev-language", "value": " zh_cn"}, {"disabled": false, "key": "dev-region", "value": " cn"}, {"disabled": false, "key": "dev-timezone", "value": " Asia/Shanghai"}, {"disabled": false, "key": "dev-app-version", "value": " 101016070"}, {"disabled": false, "key": "dev-channel", "value": " 1001"}, {"disabled": false, "key": "dev-package", "value": " com.heytap.music"}, {"disabled": false, "key": "dev-id", "value": " c28f9e5819360b7a8f4dc3f3f1ae0880"}, {"disabled": false, "key": "dev-pid", "value": " 64afe8a1cacf425fa9a3501e97f3031e"}, {"disabled": false, "key": "dev-ab-experiment", "value": " rCrNaEgHoxepLr327UJLUtMIcsTeLJJaEZ8Ag6s4P5zA-oLZVbeeE6yooeiv57QXvihV8RDINJA7NA0HQGsVSmmcST3FDhp3jKoHJbo6_agRFBax9pjf51n1ZGsZjpzC"}, {"disabled": false, "key": "dev-distinct-id", "value": " c47f8b24-1c23-4d06-aca0-2c2b54751a8a"}, {"disabled": false, "key": "dev-android-release-version", "value": " 12"}, {"disabled": false, "key": "dev-android-version", "value": " 31"}, {"disabled": false, "key": "dev-net", "value": " WIFI"}, {"disabled": false, "key": "dev-ou-id", "value": " C71B1035153C4124BBC41389BB30EACFd8d804c5a3b725cf31a20b4f48134dee"}, {"disabled": false, "key": "dev-du-id", "value": " D60ECDFAB87C4B3FA857DFFB99D83B1CEC7D424979CC9C5187152B6685735BF5"}, {"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}], "method": "GET", "baseUrl": "https://api-server.allsaints.top", "body": {}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "prerequest", "script": {"id": "preProcessors.1.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取请求接口的参数方法:https://apifox.com/help/pre-post-processors-and-scripts/scripts/api-references/pm-reference#pm-request", "// 获取请求接口的params参数，传入给python脚本调用", "var r_params = pm.request.url.query; ", "// 把数组格式的params数据转换为json字符串", "const result = r_params.reduce((obj, item) => {", "  obj[item.key] = item.value;", "  return obj;", "}, {});", "var params_str= JSON.stringify(result);", "// console.log(\"params参数:\",params_str);", "", "const res = await pm.executeAsync(\"./script/api/getSign.py\", [params_str], { command: 'python3' });", "// console.log(\"python脚本返回内容:\",res)", "// 因为python脚本返回的字符串有单引号，js转换json格式内容时需要将单引号替换为双引号", "var res1 = res.replace(/'/g, '\"');", "// /使用修正后的字符串进行 JSON 解析", "var api_params = JSON.parse(res1);", "console.log('脚本生成的api_params: ', api_params);", "// 第1种方法: 根据python脚本生成的sign内容，直接修改请求的params参数即可", "pm.request.url.query.upsert({ key: \"nonce\", value: api_params.nonce})", "pm.request.url.query.upsert({ key: \"ts\", value: api_params.ts})", "pm.request.url.query.upsert({ key: \"sign\", value: api_params.sign})", "console.log('请求时的api_params参数:',pm.request.url.getQueryString())", "", "// 第2种方法: 写入环境变量，此方案需要在接口里设置参数引用环境变量", "// pm.environment.set(\"api_params\", api_params);", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.data.songSearch.list[0].name 包含 沦陷`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data.songSearch.list[0].name`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`沦陷`);", "    const formattedValues = ____formatValues(value, compareValue, 'include');", "  ", "          pm.expect(formattedValues.value).to.include(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.data.actorSearch.list[0].name 包含 沦陷`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data.actorSearch.list[0].name`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`沦陷`);", "    const formattedValues = ____formatValues(value, compareValue, 'include');", "  ", "          pm.expect(formattedValues.value).to.include(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.2.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.data.albumSearch.list[0].name 包含 沦陷`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data.albumSearch.list[0].name`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`沦陷`);", "    const formattedValues = ____formatValues(value, compareValue, 'include');", "  ", "          pm.expect(formattedValues.value).to.include(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.3.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.data.songlistSearch.list[0].name 包含 沦陷`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data.songlistSearch.list[0].name`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`沦陷`);", "    const formattedValues = ____formatValues(value, compareValue, 'include');", "  ", "          pm.expect(formattedValues.value).to.include(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.4.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.data.videoSearch.list[0].name 包含 沦陷`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data.videoSearch.list[0].name`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`沦陷`);", "    const formattedValues = ____formatValues(value, compareValue, 'include');", "  ", "          pm.expect(formattedValues.value).to.include(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.5.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.data.moreSongSearch.list[0].name 包含 沦陷`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data.moreSongSearch.list[0].name`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`沦陷`);", "    const formattedValues = ____formatValues(value, compareValue, 'include');", "  ", "          pm.expect(formattedValues.value).to.include(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}], "responseDefinition": {"id": 561168555, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"songSearch": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "cpId": {"type": "integer"}, "coverSmall": {"type": "string"}, "coverMiddle": {"type": "string"}, "coverLarge": {"type": "string"}, "actors": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}, "files": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "fileSize": {"type": "integer"}, "bitrateType": {"type": "integer"}, "format": {"type": "string"}, "duration": {"type": "integer"}, "dv": {"type": "integer"}, "ov": {"type": "integer"}, "index": {"type": "integer"}}, "required": ["id", "fileSize", "bitrateType", "format", "duration", "dv", "ov", "index"], "x-apifox-orders": ["id", "fileSize", "bitrateType", "format", "duration", "dv", "ov", "index"]}}, "album": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}, "lyricUrl": {"type": "string"}, "favorite": {"type": "integer"}, "setAsh": {"type": "integer"}, "mv": {"type": "object", "properties": {"id": {"type": "integer"}}, "required": ["id"], "x-apifox-orders": ["id"]}, "vip": {"type": "integer"}, "priceType": {"type": "integer"}, "pureAudio": {"type": "integer"}, "commentSwitchStatus": {"type": "integer"}, "ringtoneDpLink": {"type": "string"}, "creator": {"type": "object", "properties": {}, "x-apifox-orders": []}, "ringInfos": {"type": "array", "items": {"type": "object", "properties": {"ringId": {"type": "string"}, "audioId": {"type": "integer"}}, "required": ["ringId", "audioId"], "x-apifox-orders": ["ringId", "audioId"]}}, "releaseDate": {"type": "integer"}, "ringtoneDpLinkType": {"type": "integer"}, "isOriginalSong": {"type": "integer"}, "charList": {"type": "array", "items": {"type": "string"}}, "lyric": {"type": "string"}, "oriSinger": {"type": "string"}, "labels": {"type": "array", "items": {"type": "string"}}, "aiAccompaniment": {"type": "integer"}}, "required": ["id", "name", "cpId", "coverSmall", "coverMiddle", "coverLarge", "actors", "files", "album", "lyricUrl", "favorite", "<PERSON><PERSON><PERSON>", "mv", "vip", "priceType", "pureAudio", "commentSwitchStatus", "ringtoneDpLink", "creator", "releaseDate", "ringtoneDpLinkType", "charList", "lyric", "<PERSON><PERSON><PERSON><PERSON>", "labels"], "x-apifox-orders": ["id", "name", "cpId", "coverSmall", "coverMiddle", "coverLarge", "actors", "files", "album", "lyricUrl", "favorite", "<PERSON><PERSON><PERSON>", "mv", "vip", "priceType", "pureAudio", "commentSwitchStatus", "ringtoneDpLink", "creator", "ringInfos", "releaseDate", "ringtoneDpLinkType", "isOriginalSong", "charList", "lyric", "<PERSON><PERSON><PERSON><PERSON>", "labels", "aiAccompaniment"]}}, "sortIndex": {"type": "integer"}, "totalCount": {"type": "integer"}}, "required": ["list", "sortIndex", "totalCount"], "x-apifox-orders": ["list", "sortIndex", "totalCount"]}, "moreSongSearch": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "cpId": {"type": "integer"}, "coverSmall": {"type": "string"}, "coverMiddle": {"type": "string"}, "coverLarge": {"type": "string"}, "actors": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}, "files": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "fileSize": {"type": "integer"}, "bitrateType": {"type": "integer"}, "format": {"type": "string"}, "duration": {"type": "integer"}, "dv": {"type": "integer"}, "ov": {"type": "integer"}, "index": {"type": "integer"}}, "required": ["id", "fileSize", "bitrateType", "format", "duration", "dv", "ov", "index"], "x-apifox-orders": ["id", "fileSize", "bitrateType", "format", "duration", "dv", "ov", "index"]}}, "album": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}, "lyricUrl": {"type": "string"}, "favorite": {"type": "integer"}, "setAsh": {"type": "integer"}, "mv": {"type": "object", "properties": {"id": {"type": "integer"}}, "required": ["id"], "x-apifox-orders": ["id"]}, "vip": {"type": "integer"}, "priceType": {"type": "integer"}, "pureAudio": {"type": "integer"}, "commentSwitchStatus": {"type": "integer"}, "ringtoneDpLink": {"type": "string"}, "creator": {"type": "object", "properties": {}, "x-apifox-orders": []}, "releaseDate": {"type": "integer"}, "ringtoneDpLinkType": {"type": "integer"}, "charList": {"type": "array", "items": {"type": "string"}}, "lyric": {"type": "string"}, "oriSinger": {"type": "string"}, "labels": {"type": "array", "items": {"type": "string"}}, "aiAccompaniment": {"type": "integer"}}, "required": ["id", "name", "cpId", "coverSmall", "coverMiddle", "coverLarge", "actors", "files", "album", "lyricUrl", "favorite", "<PERSON><PERSON><PERSON>", "mv", "vip", "priceType", "pureAudio", "commentSwitchStatus", "ringtoneDpLink", "creator", "releaseDate", "ringtoneDpLinkType", "charList", "lyric", "<PERSON><PERSON><PERSON><PERSON>", "labels"], "x-apifox-orders": ["id", "name", "cpId", "coverSmall", "coverMiddle", "coverLarge", "actors", "files", "album", "lyricUrl", "favorite", "<PERSON><PERSON><PERSON>", "mv", "vip", "priceType", "pureAudio", "commentSwitchStatus", "ringtoneDpLink", "creator", "releaseDate", "ringtoneDpLinkType", "charList", "lyric", "<PERSON><PERSON><PERSON><PERSON>", "labels", "aiAccompaniment"]}}, "sortIndex": {"type": "integer"}, "totalCount": {"type": "integer"}}, "required": ["list", "sortIndex", "totalCount"], "x-apifox-orders": ["list", "sortIndex", "totalCount"]}, "actorSearch": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "follow": {"type": "integer"}, "coverSmall": {"type": "string"}, "coverMiddle": {"type": "string"}, "coverLarge": {"type": "string"}, "gender": {"type": "integer"}, "pinyin": {"type": "string"}, "areaId": {"type": "integer"}, "genderId": {"type": "integer"}, "genreIds": {"type": "array", "items": {"type": "integer"}}, "songCount": {"type": "integer"}, "albumCount": {"type": "integer"}, "followTime": {"type": "integer"}, "albumName": {"type": "string"}, "releaseDate": {"type": "integer"}, "songId": {"type": "integer"}, "charList": {"type": "array", "items": {"type": "string"}}, "containMusic": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}}, "required": ["id", "name", "follow", "coverSmall", "coverMiddle", "coverLarge", "gender", "pinyin", "areaId", "genderId", "genreIds", "songCount", "albumCount", "followTime", "albumName", "releaseDate", "songId", "charList", "containMusic"], "x-apifox-orders": ["id", "name", "follow", "coverSmall", "coverMiddle", "coverLarge", "gender", "pinyin", "areaId", "genderId", "genreIds", "songCount", "albumCount", "followTime", "albumName", "releaseDate", "songId", "charList", "containMusic"]}}, "sortIndex": {"type": "integer"}, "totalCount": {"type": "integer"}}, "required": ["list", "sortIndex", "totalCount"], "x-apifox-orders": ["list", "sortIndex", "totalCount"]}, "albumSearch": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "actors": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}, "favorite": {"type": "integer"}, "cpId": {"type": "integer"}, "coverSmall": {"type": "string"}, "coverMiddle": {"type": "string"}, "coverLarge": {"type": "string"}, "commentCount": {"type": "string"}, "releaseDate": {"type": "integer"}, "releaseUnit": {"type": "string"}, "description": {"type": "string"}, "priceType": {"type": "integer"}, "tags": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}, "charList": {"type": "array", "items": {"type": "string"}}, "containMusic": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}}, "required": ["id", "name", "actors", "favorite", "cpId", "coverSmall", "coverMiddle", "coverLarge", "commentCount", "releaseDate", "releaseUnit", "description", "priceType", "tags", "charList", "containMusic"], "x-apifox-orders": ["id", "name", "actors", "favorite", "cpId", "coverSmall", "coverMiddle", "coverLarge", "commentCount", "releaseDate", "releaseUnit", "description", "priceType", "tags", "charList", "containMusic"]}}, "sortIndex": {"type": "integer"}, "totalCount": {"type": "integer"}}, "required": ["list", "sortIndex", "totalCount"], "x-apifox-orders": ["list", "sortIndex", "totalCount"]}, "songlistSearch": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "integer"}, "coverSmall": {"type": "string"}, "coverMiddle": {"type": "string"}, "coverLarge": {"type": "string"}, "playCount": {"type": "string"}, "introduction": {"type": "string"}, "favoriteCount": {"type": "string"}, "shareCount": {"type": "string"}, "creatorName": {"type": "string"}, "songCount": {"type": "integer"}, "tags": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}, "source": {"type": "string"}, "recomStatus": {"type": "integer"}, "grid": {"type": "string"}, "charList": {"type": "array", "items": {"type": "string"}}, "containMusic": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "x-apifox-orders": ["id", "name"]}}}, "required": ["id", "name", "type", "coverSmall", "coverMiddle", "coverLarge", "playCount", "introduction", "favoriteCount", "shareCount", "songCount", "tags", "source", "recomStatus", "grid", "charList", "containMusic"], "x-apifox-orders": ["id", "name", "type", "coverSmall", "coverMiddle", "coverLarge", "playCount", "introduction", "favoriteCount", "shareCount", "<PERSON><PERSON><PERSON>", "songCount", "tags", "source", "recomStatus", "grid", "charList", "containMusic"]}}, "sortIndex": {"type": "integer"}, "totalCount": {"type": "integer"}}, "required": ["list", "sortIndex", "totalCount"], "x-apifox-orders": ["list", "sortIndex", "totalCount"]}, "videoSearch": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "cpId": {"type": "integer"}, "coverSmall": {"type": "string"}, "coverMiddle": {"type": "string"}, "coverLarge": {"type": "string"}, "actors": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}, "files": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "fileSize": {"type": "integer"}, "bitrateType": {"type": "integer"}, "format": {"type": "string"}, "duration": {"type": "integer"}, "dv": {"type": "integer"}, "ov": {"type": "integer"}, "index": {"type": "integer"}, "imageWidth": {"type": "integer"}, "imageHeight": {"type": "integer"}}, "required": ["id", "fileSize", "bitrateType", "format", "duration", "dv", "ov", "index", "imageWidth", "imageHeight"], "x-apifox-orders": ["id", "fileSize", "bitrateType", "format", "duration", "dv", "ov", "index", "imageWidth", "imageHeight"]}}, "album": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}, "lyricUrl": {"type": "string"}, "favorite": {"type": "integer"}, "playCount": {"type": "string"}, "setAsh": {"type": "integer"}, "mv": {"type": "object", "properties": {"id": {"type": "integer"}}, "required": ["id"], "x-apifox-orders": ["id"]}, "vip": {"type": "integer"}, "priceType": {"type": "integer"}, "pureAudio": {"type": "integer"}, "commentSwitchStatus": {"type": "integer"}, "ringtoneDpLink": {"type": "string"}, "creator": {"type": "object", "properties": {}, "x-apifox-orders": []}, "type": {"type": "integer"}, "releaseDate": {"type": "integer"}, "ringtoneDpLinkType": {"type": "integer"}, "charList": {"type": "array", "items": {"type": "string"}}, "lyric": {"type": "string"}, "oriSinger": {"type": "string"}, "labels": {"type": "null"}}, "required": ["id", "name", "cpId", "coverSmall", "coverMiddle", "coverLarge", "actors", "files", "album", "lyricUrl", "favorite", "playCount", "<PERSON><PERSON><PERSON>", "mv", "vip", "priceType", "pureAudio", "commentSwitchStatus", "ringtoneDpLink", "creator", "releaseDate", "ringtoneDpLinkType", "charList", "lyric", "<PERSON><PERSON><PERSON><PERSON>", "labels"], "x-apifox-orders": ["id", "name", "cpId", "coverSmall", "coverMiddle", "coverLarge", "actors", "files", "album", "lyricUrl", "favorite", "playCount", "<PERSON><PERSON><PERSON>", "mv", "vip", "priceType", "pureAudio", "commentSwitchStatus", "ringtoneDpLink", "creator", "type", "releaseDate", "ringtoneDpLinkType", "charList", "lyric", "<PERSON><PERSON><PERSON><PERSON>", "labels"]}}, "sortIndex": {"type": "integer"}, "totalCount": {"type": "integer"}}, "required": ["list", "sortIndex", "totalCount"], "x-apifox-orders": ["list", "sortIndex", "totalCount"]}, "resQuality": {"type": "array", "items": {"type": "object", "properties": {"score": {"type": "string"}, "intention": {"type": "string"}, "type": {"type": "integer"}}, "required": ["score", "intention", "type"], "x-apifox-orders": ["score", "intention", "type"]}}, "ringtongSearch": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "cpId": {"type": "integer"}, "coverSmall": {"type": "string"}, "coverMiddle": {"type": "string"}, "coverLarge": {"type": "string"}, "actors": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}, "files": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "fileSize": {"type": "integer"}, "bitrateType": {"type": "integer"}, "format": {"type": "string"}, "duration": {"type": "integer"}, "dv": {"type": "integer"}, "ov": {"type": "integer"}, "index": {"type": "integer"}}, "required": ["id", "fileSize", "bitrateType", "format", "duration", "dv", "ov", "index"], "x-apifox-orders": ["id", "fileSize", "bitrateType", "format", "duration", "dv", "ov", "index"]}}, "album": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}, "lyricUrl": {"type": "string"}, "favorite": {"type": "integer"}, "setAsh": {"type": "integer"}, "mv": {"type": "object", "properties": {"id": {"type": "integer"}}, "required": ["id"], "x-apifox-orders": ["id"]}, "vip": {"type": "integer"}, "priceType": {"type": "integer"}, "pureAudio": {"type": "integer"}, "commentSwitchStatus": {"type": "integer"}, "ringtoneDpLink": {"type": "string"}, "creator": {"type": "object", "properties": {"name": {"type": "string"}}, "x-apifox-orders": ["name"]}, "ringInfos": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "channel": {"type": "string"}, "validityStart": {"type": "string"}, "validityEnd": {"type": "string"}}, "required": ["id", "channel", "validityStart", "validityEnd"], "x-apifox-orders": ["id", "channel", "validityStart", "validityEnd"]}}, "type": {"type": "integer"}, "ringtoneDpLinkType": {"type": "integer"}, "charList": {"type": "array", "items": {"type": "string"}}, "lyric": {"type": "string"}, "oriSinger": {"type": "string"}, "labels": {"type": "null"}}, "required": ["id", "name", "cpId", "coverSmall", "coverMiddle", "coverLarge", "actors", "files", "album", "lyricUrl", "favorite", "<PERSON><PERSON><PERSON>", "mv", "vip", "priceType", "pureAudio", "commentSwitchStatus", "ringtoneDpLink", "creator", "ringInfos", "type", "ringtoneDpLinkType", "charList", "lyric", "<PERSON><PERSON><PERSON><PERSON>", "labels"], "x-apifox-orders": ["id", "name", "cpId", "coverSmall", "coverMiddle", "coverLarge", "actors", "files", "album", "lyricUrl", "favorite", "<PERSON><PERSON><PERSON>", "mv", "vip", "priceType", "pureAudio", "commentSwitchStatus", "ringtoneDpLink", "creator", "ringInfos", "type", "ringtoneDpLinkType", "charList", "lyric", "<PERSON><PERSON><PERSON><PERSON>", "labels"]}}, "sortIndex": {"type": "integer"}, "totalCount": {"type": "integer"}}, "required": ["list", "sortIndex", "totalCount"], "x-apifox-orders": ["list", "sortIndex", "totalCount"]}, "podcastSearch": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"albumId": {"type": "string"}, "coverPath": {"type": "string"}, "infoSrc": {"type": "integer"}, "title": {"type": "string"}, "playVolume": {"type": "integer"}, "itemCount": {"type": "integer"}, "score": {"type": "string"}, "highlights": {"type": "array", "items": {"type": "string"}}, "isFree": {"type": "integer"}, "activity": {"type": "null"}}, "required": ["albumId", "coverPath", "infoSrc", "title", "playVolume", "itemCount", "score", "highlights", "isFree", "activity"], "x-apifox-orders": ["albumId", "coverPath", "infoSrc", "title", "playVolume", "itemCount", "score", "highlights", "isFree", "activity"]}}, "sortIndex": {"type": "integer"}, "totalCount": {"type": "integer"}}, "required": ["list", "sortIndex", "totalCount"], "x-apifox-orders": ["list", "sortIndex", "totalCount"]}}, "required": ["songSearch", "moreSongSearch", "<PERSON><PERSON><PERSON><PERSON>", "albumSearch", "songlistSearch", "videoSearch", "resQuality", "ringtongSearch", "podcastSearch"], "x-apifox-orders": ["songSearch", "moreSongSearch", "<PERSON><PERSON><PERSON><PERSON>", "albumSearch", "songlistSearch", "videoSearch", "resQuality", "ringtongSearch", "podcastSearch"]}}, "required": ["code", "message", "data"], "x-apifox-orders": ["code", "message", "data"]}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "oasExtensions": "", "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "34194f0a-fe26-4122-878d-a39a29c070d8", "id": "a474b9b3-5ad5-58b2-bbc1-5ab16545a18a", "type": "http", "name": "搜索框搜索综合内容", "projectId": 4800107, "relatedId": 6362007, "environmentId": 32208946, "blockNumber": 3, "httpApiId": 231634455, "httpApiCaseId": 255349805, "httpApiName": "搜索框搜索歌曲", "httpApiPath": "/api/v2/search/synthetical", "httpApiMethod": "get", "httpApiCaseName": "搜索框搜索歌曲"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "3c2db2b5-c499-5d7f-a925-b23672b242bf", "name": "搜索框搜索歌曲内容", "request": {"url": {"protocol": "https", "path": ["api", "v2", "search", "songList"], "host": ["api-server", "allsaints", "top"], "query": [{"disabled": false, "key": "keyword", "value": "%E6%B2%A6%E9%99%B7"}, {"disabled": false, "key": "page", "value": "1"}, {"disabled": false, "key": "pageSize", "value": "50"}, {"disabled": false, "key": "originalSearch", "value": "0"}, {"disabled": false, "key": "version", "value": "**********"}, {"disabled": false, "key": "buildVersion", "value": "***********.0_78572a8_250526"}, {"disabled": false, "key": "ak", "value": "test"}, {"disabled": false, "key": "nonce", "value": "172444063dca4a5aa66fda7e2b926dab"}, {"disabled": false, "key": "ts", "value": "1748420088351"}, {"disabled": false, "key": "sign", "value": "3496430a588f06f90ad9574b18ecadd133d89384"}], "variable": []}, "header": [{"disabled": false, "key": "dev-build-version", "value": "***********.0_78572a8_250526"}, {"disabled": false, "key": "dev-brand", "value": "OPPO"}, {"disabled": false, "key": "dev-manufacturer", "value": "OPPO"}, {"disabled": false, "key": "dev-model", "value": "PJH110"}, {"disabled": false, "key": "dev-language", "value": "zh_cn"}, {"disabled": false, "key": "dev-region", "value": "cn"}, {"disabled": false, "key": "dev-timezone", "value": "Asia/Shanghai"}, {"disabled": false, "key": "dev-app-version", "value": "101031430"}, {"disabled": false, "key": "dev-channel", "value": "1001"}, {"disabled": false, "key": "dev-package", "value": "com.oppo.music"}, {"disabled": false, "key": "dev-id", "value": "d41d8cd98f00b204e9800998ecf8427e"}, {"disabled": false, "key": "dev-pid", "value": "b5086a6bd6fc4cec874c0dd184ca8013"}, {"disabled": false, "key": "dev-ab-experiment", "value": ""}, {"disabled": false, "key": "dev-distinct-id", "value": "d624c3c2-8b23-4d67-918f-dc2a91c87d76"}, {"disabled": false, "key": "dev-android-release-version", "value": "9"}, {"disabled": false, "key": "dev-android-version", "value": "28"}, {"disabled": false, "key": "dev-net", "value": "WIFI"}, {"disabled": false, "key": "dev-ou-id", "value": ""}, {"disabled": false, "key": "dev-du-id", "value": ""}, {"disabled": false, "key": "sign-pass", "value": "1"}, {"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}], "method": "GET", "baseUrl": "https://api-server.allsaints.top", "body": {}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.code 等于 0000`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.code`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`0000`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.data.list[0].name 等于 沦陷`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data.list[0].name`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`沦陷`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}], "responseDefinition": {"id": 685866981, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"page": {"type": "integer"}, "pageSize": {"type": "integer"}, "list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "cpId": {"type": "integer"}, "coverSmall": {"type": "string"}, "coverMiddle": {"type": "string"}, "coverLarge": {"type": "string"}, "actors": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}, "files": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "fileSize": {"type": "integer"}, "bitrateType": {"type": "integer"}, "format": {"type": "string"}, "duration": {"type": "integer"}, "dv": {"type": "integer"}, "ov": {"type": "integer"}, "index": {"type": "integer"}}, "required": ["id", "fileSize", "bitrateType", "format", "duration", "dv", "ov", "index"], "x-apifox-orders": ["id", "fileSize", "bitrateType", "format", "duration", "dv", "ov", "index"]}}, "album": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}, "lyricUrl": {"type": "string"}, "favorite": {"type": "integer"}, "setAsh": {"type": "integer"}, "mv": {"type": "object", "properties": {"id": {"type": "integer"}}, "required": ["id"], "x-apifox-orders": ["id"]}, "vip": {"type": "integer"}, "priceType": {"type": "integer"}, "pureAudio": {"type": "integer"}, "commentSwitchStatus": {"type": "integer"}, "ringtoneDpLink": {"type": "string"}, "creator": {"type": "object", "properties": {}, "x-apifox-orders": []}, "ringInfos": {"type": "array", "items": {"type": "object", "properties": {"ringId": {"type": "string"}, "audioId": {"type": "integer"}}, "required": ["ringId", "audioId"], "x-apifox-orders": ["ringId", "audioId"]}}, "releaseDate": {"type": "integer"}, "ringtoneDpLinkType": {"type": "integer"}, "isOriginalSong": {"type": "integer"}, "aiAccompaniment": {"type": "integer"}, "charList": {"type": "array", "items": {"type": "string"}}, "lyric": {"type": "string"}, "oriSinger": {"type": "string"}, "labels": {"type": "array", "items": {"type": "string"}}, "songKey": {"type": "string"}, "subtitle": {"type": "string"}}, "required": ["id", "name", "cpId", "coverSmall", "coverMiddle", "coverLarge", "actors", "files", "album", "lyricUrl", "favorite", "<PERSON><PERSON><PERSON>", "mv", "vip", "priceType", "pureAudio", "commentSwitchStatus", "ringtoneDpLink", "creator", "releaseDate", "ringtoneDpLinkType", "charList", "lyric", "<PERSON><PERSON><PERSON><PERSON>", "labels"], "x-apifox-orders": ["id", "name", "cpId", "coverSmall", "coverMiddle", "coverLarge", "actors", "files", "album", "lyricUrl", "favorite", "<PERSON><PERSON><PERSON>", "mv", "vip", "priceType", "pureAudio", "commentSwitchStatus", "ringtoneDpLink", "creator", "ringInfos", "releaseDate", "ringtoneDpLinkType", "isOriginalSong", "aiAccompaniment", "charList", "lyric", "<PERSON><PERSON><PERSON><PERSON>", "labels", "<PERSON><PERSON><PERSON>", "subtitle"]}}, "quality": {"type": "object", "properties": {"score": {"type": "string"}, "intention": {"type": "string"}, "type": {"type": "integer"}}, "required": ["score", "intention", "type"], "x-apifox-orders": ["score", "intention", "type"]}}, "required": ["page", "pageSize", "list", "quality"], "x-apifox-orders": ["page", "pageSize", "list", "quality"]}}, "required": ["code", "message", "data"], "x-apifox-orders": ["code", "message", "data"]}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "oasExtensions": "", "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "f1948663-fe8e-480e-9f3a-7a0d002c8fcc", "id": "3c2db2b5-c499-5d7f-a925-b23672b242bf", "type": "http", "name": "搜索框搜索歌曲内容", "projectId": 4800107, "relatedId": 6362007, "environmentId": 32208946, "blockNumber": 4, "httpApiId": 301936538, "httpApiCaseId": 265912656, "httpApiName": "搜索框搜索歌曲内容", "httpApiPath": "/api/v2/search/songList", "httpApiMethod": "get", "httpApiCaseName": "搜索框搜索歌曲内容"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "3b99b6a1-728d-5b81-b5f2-6b6f97219c9c", "name": "搜索框搜索视频内容", "request": {"url": {"protocol": "https", "path": ["api", "v2", "search", "videoList"], "host": ["api-server", "allsaints", "top"], "query": [{"disabled": false, "key": "keyword", "value": "%E6%B2%A6%E9%99%B7"}, {"disabled": false, "key": "page", "value": "1"}, {"disabled": false, "key": "pageSize", "value": "50"}, {"disabled": false, "key": "originalSearch", "value": "0"}, {"disabled": false, "key": "isMinor", "value": "0"}, {"disabled": false, "key": "version", "value": "**********"}, {"disabled": false, "key": "buildVersion", "value": "***********.0_78572a8_250526"}, {"disabled": false, "key": "ak", "value": "test"}, {"disabled": false, "key": "nonce", "value": "9f724694f1f543d5ba216d1960b8a26e"}, {"disabled": false, "key": "ts", "value": "1748420146282"}, {"disabled": false, "key": "sign", "value": "e26a563002e8d4e3d8588993bfd7045b4d1d4c63"}], "variable": []}, "header": [{"disabled": false, "key": "dev-build-version", "value": "***********.0_78572a8_250526"}, {"disabled": false, "key": "dev-brand", "value": "OPPO"}, {"disabled": false, "key": "dev-manufacturer", "value": "OPPO"}, {"disabled": false, "key": "dev-model", "value": "PJH110"}, {"disabled": false, "key": "dev-language", "value": "zh_cn"}, {"disabled": false, "key": "dev-region", "value": "cn"}, {"disabled": false, "key": "dev-timezone", "value": "Asia/Shanghai"}, {"disabled": false, "key": "dev-app-version", "value": "101031430"}, {"disabled": false, "key": "dev-channel", "value": "1001"}, {"disabled": false, "key": "dev-package", "value": "com.oppo.music"}, {"disabled": false, "key": "dev-id", "value": "d41d8cd98f00b204e9800998ecf8427e"}, {"disabled": false, "key": "dev-pid", "value": "b5086a6bd6fc4cec874c0dd184ca8013"}, {"disabled": false, "key": "dev-ab-experiment", "value": ""}, {"disabled": false, "key": "dev-distinct-id", "value": "d624c3c2-8b23-4d67-918f-dc2a91c87d76"}, {"disabled": false, "key": "dev-android-release-version", "value": "9"}, {"disabled": false, "key": "dev-android-version", "value": "28"}, {"disabled": false, "key": "dev-net", "value": "WIFI"}, {"disabled": false, "key": "dev-ou-id", "value": ""}, {"disabled": false, "key": "dev-du-id", "value": ""}, {"disabled": false, "key": "sign-pass", "value": "1"}, {"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}], "method": "GET", "baseUrl": "https://api-server.allsaints.top", "body": {}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.code 等于 0000`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.code`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`0000`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.data.list[0].name 等于 沦陷`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data.list[0].name`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`沦陷`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}], "responseDefinition": {"id": 685926356, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "oasExtensions": "", "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "3683c5ec-c071-4aae-bef4-7734d45ec1ad", "id": "3b99b6a1-728d-5b81-b5f2-6b6f97219c9c", "type": "http", "name": "搜索框搜索视频内容", "projectId": 4800107, "relatedId": 6362007, "environmentId": 32208946, "blockNumber": 5, "httpApiId": 301936998, "httpApiCaseId": 265912655, "httpApiName": "搜索框搜索视频内容", "httpApiPath": "/api/v2/search/videoList", "httpApiMethod": "get", "httpApiCaseName": "搜索框搜索视频内容"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "cab5e4dd-99d5-51f3-bc69-1399b8c75772", "name": "搜索框搜索歌手内容", "request": {"url": {"protocol": "https", "path": ["api", "v2", "search", "actorList"], "host": ["api-server", "allsaints", "top"], "query": [{"disabled": false, "key": "keyword", "value": "%E6%B2%A6%E9%99%B7"}, {"disabled": false, "key": "page", "value": "1"}, {"disabled": false, "key": "pageSize", "value": "50"}, {"disabled": false, "key": "originalSearch", "value": "0"}, {"disabled": false, "key": "version", "value": "**********"}, {"disabled": false, "key": "buildVersion", "value": "***********.0_78572a8_250526"}, {"disabled": false, "key": "ak", "value": "test"}, {"disabled": false, "key": "nonce", "value": "012203c8c95a463db71ba707f968399d"}, {"disabled": false, "key": "ts", "value": "1748420172352"}, {"disabled": false, "key": "sign", "value": "baed9dd5acafb273dedf551217c7d9add4d6c18b"}], "variable": []}, "header": [{"disabled": false, "key": "dev-build-version", "value": "***********.0_78572a8_250526"}, {"disabled": false, "key": "dev-brand", "value": "OPPO"}, {"disabled": false, "key": "dev-manufacturer", "value": "OPPO"}, {"disabled": false, "key": "dev-model", "value": "PJH110"}, {"disabled": false, "key": "dev-language", "value": "zh_cn"}, {"disabled": false, "key": "dev-region", "value": "cn"}, {"disabled": false, "key": "dev-timezone", "value": "Asia/Shanghai"}, {"disabled": false, "key": "dev-app-version", "value": "101031430"}, {"disabled": false, "key": "dev-channel", "value": "1001"}, {"disabled": false, "key": "dev-package", "value": "com.oppo.music"}, {"disabled": false, "key": "dev-id", "value": "d41d8cd98f00b204e9800998ecf8427e"}, {"disabled": false, "key": "dev-pid", "value": "b5086a6bd6fc4cec874c0dd184ca8013"}, {"disabled": false, "key": "dev-ab-experiment", "value": ""}, {"disabled": false, "key": "dev-distinct-id", "value": "d624c3c2-8b23-4d67-918f-dc2a91c87d76"}, {"disabled": false, "key": "dev-android-release-version", "value": "9"}, {"disabled": false, "key": "dev-android-version", "value": "28"}, {"disabled": false, "key": "dev-net", "value": "WIFI"}, {"disabled": false, "key": "dev-ou-id", "value": ""}, {"disabled": false, "key": "dev-du-id", "value": ""}, {"disabled": false, "key": "sign-pass", "value": "1"}, {"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}], "method": "GET", "baseUrl": "https://api-server.allsaints.top", "body": {}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.code 等于 0000`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.code`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`0000`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.data.list[0].name 等于 沦陷`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data.list[0].name`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`沦陷`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}], "responseDefinition": {"id": 685918658, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"page": {"type": "integer"}, "pageSize": {"type": "integer"}, "list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "follow": {"type": "integer"}, "coverSmall": {"type": "string"}, "coverMiddle": {"type": "string"}, "coverLarge": {"type": "string"}, "gender": {"type": "integer"}, "pinyin": {"type": "string"}, "areaId": {"type": "integer"}, "genderId": {"type": "integer"}, "genreIds": {"type": "array", "items": {"type": "integer"}}, "songCount": {"type": "integer"}, "albumCount": {"type": "integer"}, "followTime": {"type": "integer"}, "albumName": {"type": "string"}, "releaseDate": {"type": "integer"}, "songId": {"type": "integer"}, "charList": {"type": "array", "items": {"type": "string"}}, "containMusic": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"]}}}, "required": ["id", "name", "follow", "coverSmall", "coverMiddle", "coverLarge", "gender", "pinyin", "areaId", "genderId", "genreIds", "songCount", "albumCount", "followTime", "albumName", "releaseDate", "songId", "charList", "containMusic"]}}, "quality": {"type": "object", "properties": {"score": {"type": "string"}, "intention": {"type": "string"}, "type": {"type": "integer"}}, "required": ["score", "intention", "type"]}}, "required": ["page", "pageSize", "list", "quality"]}}, "required": ["code", "message", "data"]}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "oasExtensions": "", "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "de25e50b-7c40-40d0-b7de-c921b87d425a", "id": "cab5e4dd-99d5-51f3-bc69-1399b8c75772", "type": "http", "name": "搜索框搜索歌手内容", "projectId": 4800107, "relatedId": 6362007, "environmentId": 32208946, "blockNumber": 6, "httpApiId": 301937496, "httpApiCaseId": 265912654, "httpApiName": "搜索框搜索歌手内容", "httpApiPath": "/api/v2/search/actorList", "httpApiMethod": "get", "httpApiCaseName": "搜索框搜索歌手内容"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "e39ae424-346a-5b71-913b-407abfa81af9", "name": "搜索框搜索歌单内容", "request": {"url": {"protocol": "https", "path": ["api", "v2", "search", "songlistList"], "host": ["api-server", "allsaints", "top"], "query": [{"disabled": false, "key": "keyword", "value": "%E6%B2%A6%E9%99%B7"}, {"disabled": false, "key": "page", "value": "1"}, {"disabled": false, "key": "pageSize", "value": "50"}, {"disabled": false, "key": "originalSearch", "value": "0"}, {"disabled": false, "key": "version", "value": "**********"}, {"disabled": false, "key": "buildVersion", "value": "***********.0_78572a8_250526"}, {"disabled": false, "key": "ak", "value": "test"}, {"disabled": false, "key": "nonce", "value": "fd992e1182d347f3b940fa0f56704dc4"}, {"disabled": false, "key": "ts", "value": "1748420218300"}, {"disabled": false, "key": "sign", "value": "1c855c4a9be25b3fe8e5c4b1b2418010d11c4a70"}], "variable": []}, "header": [{"disabled": false, "key": "dev-build-version", "value": "***********.0_78572a8_250526"}, {"disabled": false, "key": "dev-brand", "value": "OPPO"}, {"disabled": false, "key": "dev-manufacturer", "value": "OPPO"}, {"disabled": false, "key": "dev-model", "value": "PJH110"}, {"disabled": false, "key": "dev-language", "value": "zh_cn"}, {"disabled": false, "key": "dev-region", "value": "cn"}, {"disabled": false, "key": "dev-timezone", "value": "Asia/Shanghai"}, {"disabled": false, "key": "dev-app-version", "value": "101031430"}, {"disabled": false, "key": "dev-channel", "value": "1001"}, {"disabled": false, "key": "dev-package", "value": "com.oppo.music"}, {"disabled": false, "key": "dev-id", "value": "d41d8cd98f00b204e9800998ecf8427e"}, {"disabled": false, "key": "dev-pid", "value": "b5086a6bd6fc4cec874c0dd184ca8013"}, {"disabled": false, "key": "dev-ab-experiment", "value": ""}, {"disabled": false, "key": "dev-distinct-id", "value": "d624c3c2-8b23-4d67-918f-dc2a91c87d76"}, {"disabled": false, "key": "dev-android-release-version", "value": "9"}, {"disabled": false, "key": "dev-android-version", "value": "28"}, {"disabled": false, "key": "dev-net", "value": "WIFI"}, {"disabled": false, "key": "dev-ou-id", "value": ""}, {"disabled": false, "key": "dev-du-id", "value": ""}, {"disabled": false, "key": "sign-pass", "value": "1"}, {"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}], "method": "GET", "baseUrl": "https://api-server.allsaints.top", "body": {}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.code 等于 0000`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.code`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`0000`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.data.list[0].name 包含 沦陷`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data.list[0].name`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`沦陷`);", "    const formattedValues = ____formatValues(value, compareValue, 'include');", "  ", "          pm.expect(formattedValues.value).to.include(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}], "responseDefinition": {"id": 685929097, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"page": {"type": "integer"}, "pageSize": {"type": "integer"}, "list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "integer"}, "coverSmall": {"type": "string"}, "coverMiddle": {"type": "string"}, "coverLarge": {"type": "string"}, "playCount": {"type": "string"}, "introduction": {"type": "string"}, "favoriteCount": {"type": "string"}, "shareCount": {"type": "string"}, "songCount": {"type": "integer"}, "tags": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}, "source": {"type": "string"}, "recomStatus": {"type": "integer"}, "grid": {"type": "string"}, "charList": {"type": "array", "items": {"type": "string"}}, "containMusic": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}, "creatorName": {"type": "string"}}, "required": ["id", "name", "type", "coverSmall", "coverMiddle", "coverLarge", "playCount", "introduction", "favoriteCount", "shareCount", "songCount", "tags", "source", "recomStatus", "grid", "charList", "containMusic"], "x-apifox-orders": ["id", "name", "type", "coverSmall", "coverMiddle", "coverLarge", "playCount", "introduction", "favoriteCount", "shareCount", "songCount", "tags", "source", "recomStatus", "grid", "charList", "containMusic", "<PERSON><PERSON><PERSON>"]}}, "quality": {"type": "object", "properties": {"score": {"type": "string"}, "intention": {"type": "string"}, "type": {"type": "integer"}}, "required": ["score", "intention", "type"], "x-apifox-orders": ["score", "intention", "type"]}}, "required": ["page", "pageSize", "list", "quality"], "x-apifox-orders": ["page", "pageSize", "list", "quality"]}}, "required": ["code", "message", "data"], "x-apifox-orders": ["code", "message", "data"]}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "oasExtensions": "", "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "e716c37c-daa9-4eab-bdab-0a8db4513a3b", "id": "e39ae424-346a-5b71-913b-407abfa81af9", "type": "http", "name": "搜索框搜索歌单内容", "projectId": 4800107, "relatedId": 6362007, "environmentId": 32208946, "blockNumber": 7, "httpApiId": 301938248, "httpApiCaseId": 265912653, "httpApiName": "搜索框搜索歌单内容", "httpApiPath": "/api/v2/search/songlistList", "httpApiMethod": "get", "httpApiCaseName": "搜索框搜索歌单内容"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "1e7d529d-613e-5466-863f-e1e99431b1ca", "name": "搜索框搜索专辑内容", "request": {"url": {"protocol": "https", "path": ["api", "v2", "search", "albumList"], "host": ["api-server", "allsaints", "top"], "query": [{"disabled": false, "key": "keyword", "value": "%E6%B2%A6%E9%99%B7"}, {"disabled": false, "key": "page", "value": "1"}, {"disabled": false, "key": "pageSize", "value": "50"}, {"disabled": false, "key": "originalSearch", "value": "0"}, {"disabled": false, "key": "version", "value": "**********"}, {"disabled": false, "key": "buildVersion", "value": "***********.0_78572a8_250526"}, {"disabled": false, "key": "ak", "value": "test"}, {"disabled": false, "key": "nonce", "value": "6d252afa16d942248540d134b1ee85b2"}, {"disabled": false, "key": "ts", "value": "1748420256036"}, {"disabled": false, "key": "sign", "value": "764c6148aa0b2686ff22e8dd35d14c4b3d99c536"}], "variable": []}, "header": [{"disabled": false, "key": "dev-build-version", "value": "***********.0_78572a8_250526"}, {"disabled": false, "key": "dev-brand", "value": "OPPO"}, {"disabled": false, "key": "dev-manufacturer", "value": "OPPO"}, {"disabled": false, "key": "dev-model", "value": "PJH110"}, {"disabled": false, "key": "dev-language", "value": "zh_cn"}, {"disabled": false, "key": "dev-region", "value": "cn"}, {"disabled": false, "key": "dev-timezone", "value": "Asia/Shanghai"}, {"disabled": false, "key": "dev-app-version", "value": "101031430"}, {"disabled": false, "key": "dev-channel", "value": "1001"}, {"disabled": false, "key": "dev-package", "value": "com.oppo.music"}, {"disabled": false, "key": "dev-id", "value": "d41d8cd98f00b204e9800998ecf8427e"}, {"disabled": false, "key": "dev-pid", "value": "b5086a6bd6fc4cec874c0dd184ca8013"}, {"disabled": false, "key": "dev-ab-experiment", "value": ""}, {"disabled": false, "key": "dev-distinct-id", "value": "d624c3c2-8b23-4d67-918f-dc2a91c87d76"}, {"disabled": false, "key": "dev-android-release-version", "value": "9"}, {"disabled": false, "key": "dev-android-version", "value": "28"}, {"disabled": false, "key": "dev-net", "value": "WIFI"}, {"disabled": false, "key": "dev-ou-id", "value": ""}, {"disabled": false, "key": "dev-du-id", "value": ""}, {"disabled": false, "key": "sign-pass", "value": "1"}, {"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}], "method": "GET", "baseUrl": "https://api-server.allsaints.top", "body": {}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.code 等于 0000`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.code`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`0000`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.data.list[0].name 等于 沦陷`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data.list[0].name`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`沦陷`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}], "responseDefinition": {"id": 685937285, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"page": {"type": "integer"}, "pageSize": {"type": "integer"}, "list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "actors": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"]}}, "favorite": {"type": "integer"}, "cpId": {"type": "integer"}, "coverSmall": {"type": "string"}, "coverMiddle": {"type": "string"}, "coverLarge": {"type": "string"}, "commentCount": {"type": "string"}, "releaseDate": {"type": "integer"}, "releaseUnit": {"type": "string"}, "description": {"type": "string"}, "priceType": {"type": "integer"}, "tags": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"]}}, "charList": {"type": "array", "items": {"type": "string"}}, "containMusic": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"]}}}, "required": ["id", "name", "actors", "favorite", "cpId", "coverSmall", "coverMiddle", "coverLarge", "commentCount", "releaseDate", "releaseUnit", "description", "priceType", "tags", "charList", "containMusic"]}}, "quality": {"type": "object", "properties": {"score": {"type": "string"}, "intention": {"type": "string"}, "type": {"type": "integer"}}, "required": ["score", "intention", "type"]}}, "required": ["page", "pageSize", "list", "quality"]}}, "required": ["code", "message", "data"]}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "oasExtensions": "", "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "725f5f21-6b83-4807-94e5-451a86d9b4fe", "id": "1e7d529d-613e-5466-863f-e1e99431b1ca", "type": "http", "name": "搜索框搜索专辑内容", "projectId": 4800107, "relatedId": 6362007, "environmentId": 32208946, "blockNumber": 8, "httpApiId": 301938647, "httpApiCaseId": 265912652, "httpApiName": "搜索框搜索专辑内容", "httpApiPath": "/api/v2/search/albumList", "httpApiMethod": "get", "httpApiCaseName": "搜索框搜索专辑内容"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "a9426357-b612-5fbd-b69c-e29e72f41547", "type": "group", "metaInfo": {"id": "a9426357-b612-5fbd-b69c-e29e72f41547", "type": "group", "scopeType": "end", "scopeStartId": "be02b6d4-f51d-5714-a768-9ae23c70b879"}}], "name": "OPPO灾备环境主流程验证"}], "info": {"name": "OPPO灾备环境主流程验证"}, "dataSchemas": {}, "mockRules": {"rules": [], "enableSystemRule": true}, "environment": {"id": 32208946, "name": "华为云灾备环境", "baseUrl": "", "baseUrls": {"default": "", "308c62cb-9cc2-418f-bb3b-974a33462e34": "", "a47196e6-7551-487d-b543-232e55e09683": "https://api-server.allsaints.top", "21211e65-3c46-4c63-b963-7a7a65929263": "", "d840ff7e-32d9-4497-8809-f2f124dde270": "", "7bd03f4f-8d2d-47f1-b108-ba5e5bffdbfa": "", "5c1c2eb9-bcf3-4222-95ba-8f635ec27bb3": "", "8a734824-b5a5-4c36-bdf0-233a7d3dfe54": "", "c13fae2a-5dd2-4045-b574-725d20ccf2d6": "", "84bc06a8-d32b-48b7-b0e2-9ed78b05147c": "", "a6d5521c-eb4f-416c-ba0d-2cec8a5b9525": "", "48734eb2-f8a6-4d94-ba7c-8f480ed34ac7": "", "5266cc13-50f6-459a-b0b4-e9bd4b016e4d": "", "88eb862b-ac30-4d66-bf76-a640e6a8f1d0": "", "2e740854-ab40-46d2-a3fa-a9b2fcc88b0a": "", "ec7be0b3-1eb9-4f73-aaae-f4aa5212b816": "", "eb35bc81-9704-4e80-9e55-c5f1a2fc66b2": ""}, "variable": {"id": "b2a05c78-9ef1-483a-ad9c-ef18c34f7580", "name": "华为云灾备环境", "values": []}, "requestProxyAgentSettings": {"default": {"agentId": 2}, "308c62cb-9cc2-418f-bb3b-974a33462e34": {"agentId": 2}, "a47196e6-7551-487d-b543-232e55e09683": {"agentId": 2}, "21211e65-3c46-4c63-b963-7a7a65929263": {"agentId": 2}, "d840ff7e-32d9-4497-8809-f2f124dde270": {"agentId": 2}, "7bd03f4f-8d2d-47f1-b108-ba5e5bffdbfa": {"agentId": 2}, "5c1c2eb9-bcf3-4222-95ba-8f635ec27bb3": {"agentId": 2}, "8a734824-b5a5-4c36-bdf0-233a7d3dfe54": {"agentId": 2}, "c13fae2a-5dd2-4045-b574-725d20ccf2d6": {"agentId": 2}, "84bc06a8-d32b-48b7-b0e2-9ed78b05147c": {"agentId": 2}, "a6d5521c-eb4f-416c-ba0d-2cec8a5b9525": {"agentId": 2}, "48734eb2-f8a6-4d94-ba7c-8f480ed34ac7": {"agentId": 2}, "5266cc13-50f6-459a-b0b4-e9bd4b016e4d": {"agentId": 2}, "88eb862b-ac30-4d66-bf76-a640e6a8f1d0": {"agentId": 2}, "2e740854-ab40-46d2-a3fa-a9b2fcc88b0a": {"agentId": 2}, "ec7be0b3-1eb9-4f73-aaae-f4aa5212b816": {"agentId": 2}, "eb35bc81-9704-4e80-9e55-c5f1a2fc66b2": {"agentId": 2}}, "type": "normal", "parameter": {"header": [], "query": [], "body": [], "cookie": []}}, "globals": {"baseUrl": "", "baseUrls": {}, "variable": {"id": "883a51bf-f541-4920-8420-7ffde1d57a9f", "values": [{"type": "any", "value": "", "key": "access_token", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "uid", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "region", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "channel", "isBindInitial": true, "initialValue": ""}]}, "parameter": {"header": [], "query": [], "body": [], "cookie": []}}, "isServerBuild": false, "isTestFlowControl": true, "projectOptions": {"enableJsonc": false, "enableBigint": false, "responseValidate": true, "isDefaultUrlEncoding": 2, "enableTestScenarioSetting": false, "enableYAPICompatScript": false, "publishedDocUrlRules": {"defaultRule": "RESOURCE_KEY_ONLY", "resourceKeyStandard": "LEGACY"}, "folderShareExpandModeSettings": {"expandId": [], "mode": "AUTO"}, "mockSettings": {"engine": "fakerjs"}, "language": "zh-CN"}}
# Apifox 测试报告解析工具

这个工具可以解析 Apifox 生成的 HTML 测试报告文件，并将其转换为结构化的 Python 字典数据。

## 功能特性

- 解析测试基本信息（测试场景、运行时间、运行工具等）
- 提取统计数据（请求数、断言数、成功率等）
- 解析每个测试用例的详细信息
- 提取请求和响应数据
- 支持断言结果解析
- 输出为 JSON 格式便于后续处理

## 安装依赖

```bash
pip install beautifulsoup4
```

## 使用方法

### 基本用法

```python
from msg.parse_report import parse_apifox_report

# 解析报告文件
report_data = parse_apifox_report("apifox-reports/auto_test_oppo_23.html")

# 访问解析结果
print(f"测试场景: {report_data['basic_info']['test_scenario']}")
print(f"总请求数: {report_data['statistics']['http_requests_total']}")
print(f"失败请求数: {report_data['statistics']['http_requests_failed']}")
print(f"测试用例数量: {len(report_data['test_cases'])}")
```

### 简化用法

```python
from msg.parse_report import simple_parse_example

# 使用简化函数
result = simple_parse_example("apifox-reports/auto_test_oppo_23.html")
```

### 运行示例

直接运行脚本查看完整示例：

```bash
python msg/parse_report.py
```

## 返回数据结构

解析后的数据包含以下主要部分：

```python
{
    "basic_info": {
        "test_scenario": "测试场景名称",
        "run_time": "运行时间",
        "run_tool": "运行工具",
        "total_duration": "总耗时",
        "total_response_size": "总返回数据",
        "api_request_duration": "接口请求耗时",
        "avg_api_request_duration": "平均接口请求耗时",
        "pass_rate": "通过率",
        "fail_rate": "失败率",
        "untested_rate": "未测率"
    },
    "statistics": {
        "loops_total": 1,
        "loops_failed": 0,
        "http_requests_total": 326,
        "http_requests_failed": 2,
        "assertions_total": 769,
        "assertions_failed": 4
    },
    "test_cases": [
        {
            "name": "测试用例名称",
            "method": "HTTP方法",
            "url": "请求URL",
            "duration": "耗时",
            "response_size": "响应大小",
            "status_code": 200,
            "assertions_passed": 3,
            "assertions_failed": 0,
            "assertion_details": [
                {
                    "name": "断言名称",
                    "result": "通过/失败",
                    "details": "详细信息"
                }
            ],
            "request_data": {
                "url": {...},
                "header": [...],
                "method": "POST",
                "body": {...}
            },
            "response_data": {
                "status": "OK",
                "code": 200,
                "header": [...],
                "body": "响应内容"
            }
        }
    ]
}
```

## 主要函数说明

### `parse_apifox_report(html_file_path: str) -> Dict[str, Any]`

主要的解析函数，接受HTML文件路径，返回解析后的字典数据。

### `simple_parse_example(html_file_path: str) -> Dict[str, Any]`

简化的解析示例函数，与主函数功能相同，提供更简单的调用方式。

### `print_report_summary(report_data: Dict[str, Any]) -> None`

打印报告摘要信息的辅助函数。

## 注意事项

1. 确保HTML文件路径正确
2. 文件编码应为UTF-8
3. 支持的HTML格式为Apifox生成的标准测试报告
4. 解析过程中如遇到错误会打印错误信息并返回空字典

## 示例输出

运行解析后会显示类似以下的摘要信息：

```
============================================================
Apifox 测试报告解析结果
============================================================

【基本信息】
  test_scenario: OPPO测试环境主流程验证
  run_time: 2025-06-06 18:57:28
  run_tool: Apifox-Cli v1.5.16
  total_duration: 4m 2.28s
  total_response_size: 9.68MB
  api_request_duration: 1m 3.55s
  avg_api_request_duration: 194.95ms
  pass_rate: 99.39%
  fail_rate: 0.61%
  untested_rate: 0.00%

【统计信息】
  loops_total: 1
  loops_failed: 1
  http_requests_total: 326
  http_requests_failed: 2
  assertions_total: 769
  assertions_failed: 4

【测试用例】共 326 个
  1. 获取token [POST] - 状态码: 200
  2. 新增一个一级栏目 [POST] - 状态码: 200
  3. 发布且清缓存 [PUT] - 状态码: 200
  4. 发布二级频道tab栏目 [POST] - 状态码: 200
  5. 新增栏目后提取api栏目id [GET] - 状态码: 200
  ... 还有 321 个测试用例

解析结果已保存到: parsed_report.json
```

## 文件说明

- `msg/parse_report.py` - 主要的解析脚本
- `parsed_report.json` - 解析后生成的JSON数据文件
- `README_parse_report.md` - 本说明文档

{"apifoxCli": "1.2.21", "item": [{"item": [{"id": "53a25cce-3ee6-5e40-8221-3a6d9aca66fc", "type": "group", "metaInfo": {"id": "53a25cce-3ee6-5e40-8221-3a6d9aca66fc", "type": "group", "scopeType": "start", "scopeEndId": "f8bf6a6b-70c1-5440-9c9c-086193b15e07", "name": "root"}}, {"id": "0ec581d5-a600-57e3-89b2-5db3c8a761c7", "name": "RECO-对搜索框搜索歌曲【沦陷】，查看返回内容是否正确", "request": {"url": {"protocol": "https", "path": ["api", "v2", "search", "synthetical"], "host": ["test-api-server", "allsaints", "top"], "query": [{"disabled": false, "key": "keyword", "value": "沦陷"}, {"disabled": false, "key": "originalSearch", "value": "0"}, {"disabled": false, "key": "isMinor", "value": "0"}, {"disabled": false, "key": "version", "value": "10.16.70"}, {"disabled": false, "key": "buildVersion", "value": "40.10.16.70test_6f652d0_241106"}, {"disabled": false, "key": "ak", "value": "test"}, {"disabled": false, "key": "nonce", "value": "ba6814a0a3c84e45965d4b34f4c40c9f"}, {"disabled": false, "key": "ts", "value": "1731050489591"}, {"disabled": false, "key": "uid", "value": "o_id_6CEquaHgqepM8FYNjGTjLL"}, {"disabled": false, "key": "utoken", "value": "fe50dac689034072984095ea4d4c3349"}, {"disabled": false, "key": "sign", "value": "57f90796586f52fe3c77e5cca76be9016cb017f5"}], "variable": []}, "header": [{"disabled": false, "key": "Host", "value": "{{Host}}"}, {"disabled": false, "key": "Connection", "value": " Keep-AliveAccept-Encoding: gzip"}, {"disabled": false, "key": "User-Agent", "value": " okhttp/4.12.0"}, {"disabled": false, "key": "dev-build-version", "value": " 40.10.16.70test_6f652d0_241106"}, {"disabled": false, "key": "dev-brand", "value": " OPPO"}, {"disabled": false, "key": "dev-manufacturer", "value": " OPPO"}, {"disabled": false, "key": "dev-model", "value": " PDNM00"}, {"disabled": false, "key": "dev-language", "value": " zh_cn"}, {"disabled": false, "key": "dev-region", "value": " cn"}, {"disabled": false, "key": "dev-timezone", "value": " Asia/Shanghai"}, {"disabled": false, "key": "dev-app-version", "value": " 101016070"}, {"disabled": false, "key": "dev-channel", "value": " {{channel}}"}, {"disabled": false, "key": "dev-package", "value": " com.heytap.music"}, {"disabled": false, "key": "dev-id", "value": " c28f9e5819360b7a8f4dc3f3f1ae0880"}, {"disabled": false, "key": "dev-pid", "value": " 64afe8a1cacf425fa9a3501e97f3031e"}, {"disabled": false, "key": "dev-ab-experiment", "value": " rCrNaEgHoxepLr327UJLUtMIcsTeLJJaEZ8Ag6s4P5zA-oLZVbeeE6yooeiv57QXvihV8RDINJA7NA0HQGsVSmmcST3FDhp3jKoHJbo6_agRFBax9pjf51n1ZGsZjpzC"}, {"disabled": false, "key": "dev-distinct-id", "value": " c47f8b24-1c23-4d06-aca0-2c2b54751a8a"}, {"disabled": false, "key": "dev-android-release-version", "value": " 12"}, {"disabled": false, "key": "dev-android-version", "value": " 31"}, {"disabled": false, "key": "dev-net", "value": " WIFI"}, {"disabled": false, "key": "dev-ou-id", "value": " C71B1035153C4124BBC41389BB30EACFd8d804c5a3b725cf31a20b4f48134dee"}, {"disabled": false, "key": "dev-du-id", "value": " D60ECDFAB87C4B3FA857DFFB99D83B1CEC7D424979CC9C5187152B6685735BF5"}, {"disabled": false, "key": "dev-utoken-pass", "value": "1"}], "method": "GET", "baseUrl": "https://test-api-server.allsaints.top", "body": {}, "auth": {"type": "<PERSON><PERSON><PERSON>", "noauth": []}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`测试场景-搜索对应类型数据返回正常`);", "      pm.test(formattedName, async function(done) {", "        try {", "          const value = pm.response.text();", "          ", "    const compareValue = await ____replaceIn(\"\");", "    const formattedValues = ____formatValues(value, compareValue, 'exists');", "  ", "          pm.expectWithKey({key:'undefined',value: value === null ? '' : formattedValues.value }).to.exist", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.code 等于 0000`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.code`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`0000`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.2.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`判断返回的歌曲名字是为【沦陷】`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.data.songSearch.list[0].name`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`沦陷`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}], "responseDefinition": {"id": 561168555, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"songSearch": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "cpId": {"type": "integer"}, "coverSmall": {"type": "string"}, "coverMiddle": {"type": "string"}, "coverLarge": {"type": "string"}, "actors": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}, "files": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "fileSize": {"type": "integer"}, "bitrateType": {"type": "integer"}, "format": {"type": "string"}, "duration": {"type": "integer"}, "dv": {"type": "integer"}, "ov": {"type": "integer"}, "index": {"type": "integer"}}, "required": ["id", "fileSize", "bitrateType", "format", "duration", "dv", "ov", "index"], "x-apifox-orders": ["id", "fileSize", "bitrateType", "format", "duration", "dv", "ov", "index"]}}, "album": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}, "lyricUrl": {"type": "string"}, "favorite": {"type": "integer"}, "setAsh": {"type": "integer"}, "mv": {"type": "object", "properties": {"id": {"type": "integer"}}, "required": ["id"], "x-apifox-orders": ["id"]}, "vip": {"type": "integer"}, "priceType": {"type": "integer"}, "pureAudio": {"type": "integer"}, "commentSwitchStatus": {"type": "integer"}, "ringtoneDpLink": {"type": "string"}, "creator": {"type": "object", "properties": {}, "x-apifox-orders": []}, "ringInfos": {"type": "array", "items": {"type": "object", "properties": {"ringId": {"type": "string"}, "audioId": {"type": "integer"}}, "required": ["ringId", "audioId"], "x-apifox-orders": ["ringId", "audioId"]}}, "releaseDate": {"type": "integer"}, "ringtoneDpLinkType": {"type": "integer"}, "isOriginalSong": {"type": "integer"}, "charList": {"type": "array", "items": {"type": "string"}}, "lyric": {"type": "string"}, "oriSinger": {"type": "string"}, "labels": {"type": "array", "items": {"type": "string"}}, "aiAccompaniment": {"type": "integer"}}, "required": ["id", "name", "cpId", "coverSmall", "coverMiddle", "coverLarge", "actors", "files", "album", "lyricUrl", "favorite", "<PERSON><PERSON><PERSON>", "mv", "vip", "priceType", "pureAudio", "commentSwitchStatus", "ringtoneDpLink", "creator", "releaseDate", "ringtoneDpLinkType", "charList", "lyric", "<PERSON><PERSON><PERSON><PERSON>", "labels"], "x-apifox-orders": ["id", "name", "cpId", "coverSmall", "coverMiddle", "coverLarge", "actors", "files", "album", "lyricUrl", "favorite", "<PERSON><PERSON><PERSON>", "mv", "vip", "priceType", "pureAudio", "commentSwitchStatus", "ringtoneDpLink", "creator", "ringInfos", "releaseDate", "ringtoneDpLinkType", "isOriginalSong", "charList", "lyric", "<PERSON><PERSON><PERSON><PERSON>", "labels", "aiAccompaniment"]}}, "sortIndex": {"type": "integer"}, "totalCount": {"type": "integer"}}, "required": ["list", "sortIndex", "totalCount"], "x-apifox-orders": ["list", "sortIndex", "totalCount"]}, "moreSongSearch": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "cpId": {"type": "integer"}, "coverSmall": {"type": "string"}, "coverMiddle": {"type": "string"}, "coverLarge": {"type": "string"}, "actors": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}, "files": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "fileSize": {"type": "integer"}, "bitrateType": {"type": "integer"}, "format": {"type": "string"}, "duration": {"type": "integer"}, "dv": {"type": "integer"}, "ov": {"type": "integer"}, "index": {"type": "integer"}}, "required": ["id", "fileSize", "bitrateType", "format", "duration", "dv", "ov", "index"], "x-apifox-orders": ["id", "fileSize", "bitrateType", "format", "duration", "dv", "ov", "index"]}}, "album": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}, "lyricUrl": {"type": "string"}, "favorite": {"type": "integer"}, "setAsh": {"type": "integer"}, "mv": {"type": "object", "properties": {"id": {"type": "integer"}}, "required": ["id"], "x-apifox-orders": ["id"]}, "vip": {"type": "integer"}, "priceType": {"type": "integer"}, "pureAudio": {"type": "integer"}, "commentSwitchStatus": {"type": "integer"}, "ringtoneDpLink": {"type": "string"}, "creator": {"type": "object", "properties": {}, "x-apifox-orders": []}, "releaseDate": {"type": "integer"}, "ringtoneDpLinkType": {"type": "integer"}, "charList": {"type": "array", "items": {"type": "string"}}, "lyric": {"type": "string"}, "oriSinger": {"type": "string"}, "labels": {"type": "array", "items": {"type": "string"}}, "aiAccompaniment": {"type": "integer"}}, "required": ["id", "name", "cpId", "coverSmall", "coverMiddle", "coverLarge", "actors", "files", "album", "lyricUrl", "favorite", "<PERSON><PERSON><PERSON>", "mv", "vip", "priceType", "pureAudio", "commentSwitchStatus", "ringtoneDpLink", "creator", "releaseDate", "ringtoneDpLinkType", "charList", "lyric", "<PERSON><PERSON><PERSON><PERSON>", "labels"], "x-apifox-orders": ["id", "name", "cpId", "coverSmall", "coverMiddle", "coverLarge", "actors", "files", "album", "lyricUrl", "favorite", "<PERSON><PERSON><PERSON>", "mv", "vip", "priceType", "pureAudio", "commentSwitchStatus", "ringtoneDpLink", "creator", "releaseDate", "ringtoneDpLinkType", "charList", "lyric", "<PERSON><PERSON><PERSON><PERSON>", "labels", "aiAccompaniment"]}}, "sortIndex": {"type": "integer"}, "totalCount": {"type": "integer"}}, "required": ["list", "sortIndex", "totalCount"], "x-apifox-orders": ["list", "sortIndex", "totalCount"]}, "actorSearch": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "follow": {"type": "integer"}, "coverSmall": {"type": "string"}, "coverMiddle": {"type": "string"}, "coverLarge": {"type": "string"}, "gender": {"type": "integer"}, "pinyin": {"type": "string"}, "areaId": {"type": "integer"}, "genderId": {"type": "integer"}, "genreIds": {"type": "array", "items": {"type": "integer"}}, "songCount": {"type": "integer"}, "albumCount": {"type": "integer"}, "followTime": {"type": "integer"}, "albumName": {"type": "string"}, "releaseDate": {"type": "integer"}, "songId": {"type": "integer"}, "charList": {"type": "array", "items": {"type": "string"}}, "containMusic": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}}, "required": ["id", "name", "follow", "coverSmall", "coverMiddle", "coverLarge", "gender", "pinyin", "areaId", "genderId", "genreIds", "songCount", "albumCount", "followTime", "albumName", "releaseDate", "songId", "charList", "containMusic"], "x-apifox-orders": ["id", "name", "follow", "coverSmall", "coverMiddle", "coverLarge", "gender", "pinyin", "areaId", "genderId", "genreIds", "songCount", "albumCount", "followTime", "albumName", "releaseDate", "songId", "charList", "containMusic"]}}, "sortIndex": {"type": "integer"}, "totalCount": {"type": "integer"}}, "required": ["list", "sortIndex", "totalCount"], "x-apifox-orders": ["list", "sortIndex", "totalCount"]}, "albumSearch": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "actors": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}, "favorite": {"type": "integer"}, "cpId": {"type": "integer"}, "coverSmall": {"type": "string"}, "coverMiddle": {"type": "string"}, "coverLarge": {"type": "string"}, "commentCount": {"type": "string"}, "releaseDate": {"type": "integer"}, "releaseUnit": {"type": "string"}, "description": {"type": "string"}, "priceType": {"type": "integer"}, "tags": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}, "charList": {"type": "array", "items": {"type": "string"}}, "containMusic": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}}, "required": ["id", "name", "actors", "favorite", "cpId", "coverSmall", "coverMiddle", "coverLarge", "commentCount", "releaseDate", "releaseUnit", "description", "priceType", "tags", "charList", "containMusic"], "x-apifox-orders": ["id", "name", "actors", "favorite", "cpId", "coverSmall", "coverMiddle", "coverLarge", "commentCount", "releaseDate", "releaseUnit", "description", "priceType", "tags", "charList", "containMusic"]}}, "sortIndex": {"type": "integer"}, "totalCount": {"type": "integer"}}, "required": ["list", "sortIndex", "totalCount"], "x-apifox-orders": ["list", "sortIndex", "totalCount"]}, "songlistSearch": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "integer"}, "coverSmall": {"type": "string"}, "coverMiddle": {"type": "string"}, "coverLarge": {"type": "string"}, "playCount": {"type": "string"}, "introduction": {"type": "string"}, "favoriteCount": {"type": "string"}, "shareCount": {"type": "string"}, "creatorName": {"type": "string"}, "songCount": {"type": "integer"}, "tags": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}, "source": {"type": "string"}, "recomStatus": {"type": "integer"}, "grid": {"type": "string"}, "charList": {"type": "array", "items": {"type": "string"}}, "containMusic": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "x-apifox-orders": ["id", "name"]}}}, "required": ["id", "name", "type", "coverSmall", "coverMiddle", "coverLarge", "playCount", "introduction", "favoriteCount", "shareCount", "songCount", "tags", "source", "recomStatus", "grid", "charList", "containMusic"], "x-apifox-orders": ["id", "name", "type", "coverSmall", "coverMiddle", "coverLarge", "playCount", "introduction", "favoriteCount", "shareCount", "<PERSON><PERSON><PERSON>", "songCount", "tags", "source", "recomStatus", "grid", "charList", "containMusic"]}}, "sortIndex": {"type": "integer"}, "totalCount": {"type": "integer"}}, "required": ["list", "sortIndex", "totalCount"], "x-apifox-orders": ["list", "sortIndex", "totalCount"]}, "videoSearch": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "cpId": {"type": "integer"}, "coverSmall": {"type": "string"}, "coverMiddle": {"type": "string"}, "coverLarge": {"type": "string"}, "actors": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}, "files": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "fileSize": {"type": "integer"}, "bitrateType": {"type": "integer"}, "format": {"type": "string"}, "duration": {"type": "integer"}, "dv": {"type": "integer"}, "ov": {"type": "integer"}, "index": {"type": "integer"}, "imageWidth": {"type": "integer"}, "imageHeight": {"type": "integer"}}, "required": ["id", "fileSize", "bitrateType", "format", "duration", "dv", "ov", "index", "imageWidth", "imageHeight"], "x-apifox-orders": ["id", "fileSize", "bitrateType", "format", "duration", "dv", "ov", "index", "imageWidth", "imageHeight"]}}, "album": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}, "lyricUrl": {"type": "string"}, "favorite": {"type": "integer"}, "playCount": {"type": "string"}, "setAsh": {"type": "integer"}, "mv": {"type": "object", "properties": {"id": {"type": "integer"}}, "required": ["id"], "x-apifox-orders": ["id"]}, "vip": {"type": "integer"}, "priceType": {"type": "integer"}, "pureAudio": {"type": "integer"}, "commentSwitchStatus": {"type": "integer"}, "ringtoneDpLink": {"type": "string"}, "creator": {"type": "object", "properties": {}, "x-apifox-orders": []}, "type": {"type": "integer"}, "releaseDate": {"type": "integer"}, "ringtoneDpLinkType": {"type": "integer"}, "charList": {"type": "array", "items": {"type": "string"}}, "lyric": {"type": "string"}, "oriSinger": {"type": "string"}, "labels": {"type": "null"}}, "required": ["id", "name", "cpId", "coverSmall", "coverMiddle", "coverLarge", "actors", "files", "album", "lyricUrl", "favorite", "playCount", "<PERSON><PERSON><PERSON>", "mv", "vip", "priceType", "pureAudio", "commentSwitchStatus", "ringtoneDpLink", "creator", "releaseDate", "ringtoneDpLinkType", "charList", "lyric", "<PERSON><PERSON><PERSON><PERSON>", "labels"], "x-apifox-orders": ["id", "name", "cpId", "coverSmall", "coverMiddle", "coverLarge", "actors", "files", "album", "lyricUrl", "favorite", "playCount", "<PERSON><PERSON><PERSON>", "mv", "vip", "priceType", "pureAudio", "commentSwitchStatus", "ringtoneDpLink", "creator", "type", "releaseDate", "ringtoneDpLinkType", "charList", "lyric", "<PERSON><PERSON><PERSON><PERSON>", "labels"]}}, "sortIndex": {"type": "integer"}, "totalCount": {"type": "integer"}}, "required": ["list", "sortIndex", "totalCount"], "x-apifox-orders": ["list", "sortIndex", "totalCount"]}, "resQuality": {"type": "array", "items": {"type": "object", "properties": {"score": {"type": "string"}, "intention": {"type": "string"}, "type": {"type": "integer"}}, "required": ["score", "intention", "type"], "x-apifox-orders": ["score", "intention", "type"]}}, "ringtongSearch": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "cpId": {"type": "integer"}, "coverSmall": {"type": "string"}, "coverMiddle": {"type": "string"}, "coverLarge": {"type": "string"}, "actors": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}, "files": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "fileSize": {"type": "integer"}, "bitrateType": {"type": "integer"}, "format": {"type": "string"}, "duration": {"type": "integer"}, "dv": {"type": "integer"}, "ov": {"type": "integer"}, "index": {"type": "integer"}}, "required": ["id", "fileSize", "bitrateType", "format", "duration", "dv", "ov", "index"], "x-apifox-orders": ["id", "fileSize", "bitrateType", "format", "duration", "dv", "ov", "index"]}}, "album": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}, "lyricUrl": {"type": "string"}, "favorite": {"type": "integer"}, "setAsh": {"type": "integer"}, "mv": {"type": "object", "properties": {"id": {"type": "integer"}}, "required": ["id"], "x-apifox-orders": ["id"]}, "vip": {"type": "integer"}, "priceType": {"type": "integer"}, "pureAudio": {"type": "integer"}, "commentSwitchStatus": {"type": "integer"}, "ringtoneDpLink": {"type": "string"}, "creator": {"type": "object", "properties": {"name": {"type": "string"}}, "x-apifox-orders": ["name"]}, "ringInfos": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "channel": {"type": "string"}, "validityStart": {"type": "string"}, "validityEnd": {"type": "string"}}, "required": ["id", "channel", "validityStart", "validityEnd"], "x-apifox-orders": ["id", "channel", "validityStart", "validityEnd"]}}, "type": {"type": "integer"}, "ringtoneDpLinkType": {"type": "integer"}, "charList": {"type": "array", "items": {"type": "string"}}, "lyric": {"type": "string"}, "oriSinger": {"type": "string"}, "labels": {"type": "null"}}, "required": ["id", "name", "cpId", "coverSmall", "coverMiddle", "coverLarge", "actors", "files", "album", "lyricUrl", "favorite", "<PERSON><PERSON><PERSON>", "mv", "vip", "priceType", "pureAudio", "commentSwitchStatus", "ringtoneDpLink", "creator", "ringInfos", "type", "ringtoneDpLinkType", "charList", "lyric", "<PERSON><PERSON><PERSON><PERSON>", "labels"], "x-apifox-orders": ["id", "name", "cpId", "coverSmall", "coverMiddle", "coverLarge", "actors", "files", "album", "lyricUrl", "favorite", "<PERSON><PERSON><PERSON>", "mv", "vip", "priceType", "pureAudio", "commentSwitchStatus", "ringtoneDpLink", "creator", "ringInfos", "type", "ringtoneDpLinkType", "charList", "lyric", "<PERSON><PERSON><PERSON><PERSON>", "labels"]}}, "sortIndex": {"type": "integer"}, "totalCount": {"type": "integer"}}, "required": ["list", "sortIndex", "totalCount"], "x-apifox-orders": ["list", "sortIndex", "totalCount"]}, "podcastSearch": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"albumId": {"type": "string"}, "coverPath": {"type": "string"}, "infoSrc": {"type": "integer"}, "title": {"type": "string"}, "playVolume": {"type": "integer"}, "itemCount": {"type": "integer"}, "score": {"type": "string"}, "highlights": {"type": "array", "items": {"type": "string"}}, "isFree": {"type": "integer"}, "activity": {"type": "null"}}, "required": ["albumId", "coverPath", "infoSrc", "title", "playVolume", "itemCount", "score", "highlights", "isFree", "activity"], "x-apifox-orders": ["albumId", "coverPath", "infoSrc", "title", "playVolume", "itemCount", "score", "highlights", "isFree", "activity"]}}, "sortIndex": {"type": "integer"}, "totalCount": {"type": "integer"}}, "required": ["list", "sortIndex", "totalCount"], "x-apifox-orders": ["list", "sortIndex", "totalCount"]}}, "required": ["resQuality"], "x-apifox-orders": ["songSearch", "moreSongSearch", "<PERSON><PERSON><PERSON><PERSON>", "albumSearch", "songlistSearch", "videoSearch", "resQuality", "ringtongSearch", "podcastSearch"]}}, "required": ["code", "message", "data"], "x-apifox-orders": ["code", "message", "data"]}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "oasExtensions": "", "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "de00cb31-e726-46e1-9e35-b8c3fe1bc38e", "id": "0ec581d5-a600-57e3-89b2-5db3c8a761c7", "type": "http", "name": "RECO-对搜索框搜索歌曲【沦陷】，查看返回内容是否正确", "projectId": 4800107, "relatedId": 6669821, "environmentId": 24915297, "blockNumber": 1, "httpApiId": 231634455, "httpApiCaseId": 275589656, "httpApiName": "搜索框搜索歌曲", "httpApiPath": "/api/v2/search/synthetical", "httpApiMethod": "get", "httpApiCaseName": "成功"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "5768b74c-2ce2-5186-bba1-4d1b6039fb54", "name": "RECO-获取歌曲每日推荐（大数据），提取所有id", "request": {"url": {"path": ["api", "recommend", "daily", "songs", "v2"], "host": ["gw-reco", "allsaints-internal", "com"], "query": [{"disabled": false, "key": "uid", "value": "o_id_UkEepbYhLmQ2szxBZt7Cui"}, {"disabled": false, "key": "pid", "value": "c977481f0ca3417291893593f0a1d21b"}], "variable": []}, "header": [{"disabled": false, "key": "region", "value": "CN"}, {"disabled": false, "key": "channel", "value": "{{channel}}"}, {"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}], "method": "GET", "baseUrl": "gw-reco.allsaints-internal.com", "body": {}, "auth": {"type": "<PERSON><PERSON><PERSON>", "noauth": []}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`测试场景-获取大数据每日推荐内容，查看是否与app推荐内容匹配`);", "      pm.test(formattedName, async function(done) {", "        try {", "          const value = pm.response.text();", "          ", "    const compareValue = await ____replaceIn(\"\");", "    const formattedValues = ____formatValues(value, compareValue, 'exists');", "  ", "          pm.expectWithKey({key:'undefined',value: value === null ? '' : formattedValues.value }).to.exist", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.code 等于 0000`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.code`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`0000`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.2.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        var jsonData = pm.response.json();", "var recommendID = [];", "var len = jsonData.data.length;", "", "for(i = 0; i < len; i++)", "{", "    recommendID.push(jsonData.data[i].id);", "}", "", "console.log(\"recommendID:\"+recommendID);", "", "pm.variables.set(\"recommendID\",recommendID);", "      "]}}], "responseDefinition": {"id": 588800653, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": ["string", "null"]}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "tag": {"type": "integer"}, "tag_type": {"type": "integer"}, "tag_name": {"type": "string"}, "link": {"type": "string"}, "algo": {"type": "object", "properties": {"type": {"type": "string"}, "score": {"anyOf": [{"type": "integer"}, {"type": "number"}]}}, "required": ["type", "score"], "x-apifox-orders": ["type", "score"]}}, "required": ["id", "tag", "tag_type", "tag_name", "link", "algo"], "x-apifox-orders": ["id", "tag", "tag_type", "tag_name", "link", "algo"]}}}, "required": ["code", "message", "data"], "x-apifox-orders": ["code", "message", "data"]}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "oasExtensions": "", "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "33e3cb0b-59ad-4b1b-9bab-09ea6d66ac2b", "id": "5768b74c-2ce2-5186-bba1-4d1b6039fb54", "type": "http", "name": "RECO-获取歌曲每日推荐（大数据），提取所有id", "projectId": 4800107, "relatedId": 6669820, "environmentId": 24915297, "blockNumber": 1, "httpApiId": 246508448, "httpApiCaseId": 275589724, "httpApiName": "大数据-歌曲每日推荐", "httpApiPath": "/api/recommend/daily/songs/v2", "httpApiMethod": "get", "httpApiCaseName": "大数据-歌曲每日推荐"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "36508579-8954-5b4a-aaef-00635ddbb185", "type": "if", "metaInfo": {"type": "if", "scopeType": "start", "scopeEndId": "945fadf4-582d-529a-928d-66e966512af4", "events": [{"description": "{{recommendID}} notEqual undefined", "listen": "test", "script": {"exec": "\n\n  ____string2Array = function(value) {\n    if(typeof value === 'object'){\n      return value;\n    }\n    try {\n      return JSON.parse(value);\n    } catch (e) {\n      return value;\n    }\n  }\n  ____string2Number = function(value, errorMsg) {\n   if(typeof value !== 'string'){\n     return value;\n   }\n   if (/^\\-?\\d+$/.test(value)) {\n       return parseInt(value);\n   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {\n       return parseFloat(value);\n   } else {\n       throw new Error(errorMsg || '数据类型不匹配')\n   }\n }\n\n  ____formatValues = function(value, stringCompareValue, comparison) {\n   try{\n     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);\n     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);\n     let compareValue;\n     switch (typeof value) {\n         case 'string':\n             if (isNumberComparisons) {\n                value = ____string2Number(value);\n                compareValue = ____string2Number(stringCompareValue);\n             } else if (isCollectionComparisons) {\n                compareValue = ____string2Array(stringCompareValue);\n             } else if (comparison === 'exists' || comparison === 'notExist') {\n                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;\n             } else {\n                compareValue = stringCompareValue;\n             }\n             break;\n         case 'object':\n             const isArray = value instanceof Array;\n             if (isNumberComparisons) {\n                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')\n             } else if (isCollectionComparisons && isArray) {\n              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')\n            } else if (\n              isArray &&\n              comparison === 'include' &&\n              value.includes(stringCompareValue)\n            ) {\n              compareValue = stringCompareValue;\n            } else {\n              try {\n                  compareValue = JSON.parse(stringCompareValue);\n              } catch (e) {\n                  compareValue = stringCompareValue;\n              }\n            }\n             break;\n         case 'boolean':\n             if (isNumberComparisons || isCollectionComparisons) {\n                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')\n             }\n             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);\n             break;\n           case 'bigint':\n           case 'number':\n             if (isNumberComparisons) {\n               compareValue = ____string2Number(stringCompareValue);\n             } else if (isCollectionComparisons) {\n              compareValue = ____string2Array(stringCompareValue);\n              value = '' + value;\n            } else {\n               compareValue = stringCompareValue;\n               value = '' + value;\n             }\n             break;\n         case 'null':\n             if (isNumberComparisons) {\n                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')\n             }\n             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;\n             break;\n         default:\n            if (isCollectionComparisons) {\n              compareValue = ____string2Array(stringCompareValue);\n            } else {\n              compareValue = stringCompareValue;\n            }\n            break;\n     }\n     return { compareValue, value };\n   } catch(e) {\n     console.log(e);\n     throw e;\n   }\n }\n \n\n        if (JSON.setEnableBigInt) {\n          JSON.setEnableBigInt(undefined);\n        }\n        \n  async function ____replaceIn(value) {\n    if (typeof pm.variables.replaceInAsync === 'function') {\n      return await pm.variables.replaceInAsync(value);\n    }\n    return pm.variables.replaceIn(value);\n  };\n\n  ;(async () => {\n    try {\n      const formattedName = await ____replaceIn(`undefined 不等于 undefined`);\n      pm.test(formattedName, async function(done) {\n        try {\n          const value = await ____replaceIn(`{{recommendID}}`);\n          \n    const compareValue = await ____replaceIn(`undefined`);\n    const formattedValues = ____formatValues(value, compareValue, 'notEqual');\n  \n          pm.expect(formattedValues.value).to.not.eql(formattedValues.compareValue);\n          done();\n        } catch (err) {\n          done(err);\n        }\n      });\n    } catch(e) {\n      setImmediate(() => { throw e });\n    }\n  })();\n      ", "id": "if.0.assertion", "type": "text/javascript"}}]}}, {"id": "584a03cd-99e2-5533-b839-b41c5fa51c22", "type": "delay", "metaInfo": {"id": "584a03cd-99e2-5533-b839-b41c5fa51c22", "type": "delay", "timeout": 2000}}, {"id": "57ac3e50-a915-5099-b84e-369b569c2a4f", "name": "查看app每日推荐内容与大数据内容是否匹配", "request": {"url": {"protocol": "https", "path": ["api", "v2", "recommend", "dailySongTags"], "host": ["test-api-server", "allsaints", "top"], "query": [{"disabled": false, "key": "version", "value": "10.31.0"}, {"disabled": false, "key": "buildVersion", "value": "40.10.31.0test_d609ec8_241213"}, {"disabled": false, "key": "ak", "value": "test"}, {"disabled": false, "key": "nonce", "value": "8aa93e457f4d408d9511d67587d4ba14"}, {"disabled": false, "key": "ts", "value": "1734596812542"}, {"disabled": false, "key": "uid", "value": "o_id_UkEepbYhLmQ2szxBZt7Cui"}, {"disabled": false, "key": "utoken", "value": "8fdce6206b234519849f89b33d67b965"}, {"disabled": false, "key": "sign", "value": "4d3a47ce03eaac8e72914e6c4baaefedc7ad5f1f"}], "variable": []}, "header": [{"disabled": false, "key": "Host", "value": "{{Host}}"}, {"disabled": false, "key": "Connection", "value": " Keep-Alive"}, {"disabled": false, "key": "Accept-Encoding", "value": " gzip"}, {"disabled": false, "key": "User-Agent", "value": " okhttp/4.12.0"}, {"disabled": false, "key": "dev-build-version", "value": " 40.10.31.0test_d609ec8_241213"}, {"disabled": false, "key": "dev-brand", "value": " OPPO"}, {"disabled": false, "key": "dev-manufacturer", "value": " OPPO"}, {"disabled": false, "key": "dev-model", "value": " PDNM00"}, {"disabled": false, "key": "dev-language", "value": " zh_cn"}, {"disabled": false, "key": "dev-region", "value": " cn"}, {"disabled": false, "key": "dev-timezone", "value": " Asia/Shanghai"}, {"disabled": false, "key": "dev-app-version", "value": " 101031000"}, {"disabled": false, "key": "dev-channel", "value": " {{channel}}"}, {"disabled": false, "key": "dev-package", "value": " com.heytap.music"}, {"disabled": false, "key": "dev-id", "value": " c28f9e5819360b7a8f4dc3f3f1ae0880"}, {"disabled": false, "key": "dev-pid", "value": " c977481f0ca3417291893593f0a1d21b"}, {"disabled": false, "key": "dev-ab-experiment", "value": " r8rwotOO5ZSOOFcTLj8bBPmiv4V3Fh2NL91nqnQyl3GLVGifZKplZtClyZ_ibsQgDKb0cfuMoqWrnXrHAv7pvr-3TvoX3gGiaWoTJ2B5xxxRwTuyBcYPR61vxsaGuf56rsquHAV0d1Crylj-U9K4Jp7UU4viM6fBNnN1kP-kxZs="}, {"disabled": false, "key": "dev-distinct-id", "value": " 42cb6575-9718-4cbb-bab6-07a85235935e"}, {"disabled": false, "key": "dev-android-release-version", "value": " 12"}, {"disabled": false, "key": "dev-android-version", "value": " 31"}, {"disabled": false, "key": "dev-net", "value": " WIFI"}, {"disabled": false, "key": "dev-ou-id", "value": " C71B1035153C4124BBC41389BB30EACFd8d804c5a3b725cf31a20b4f48134dee"}, {"disabled": false, "key": "dev-du-id", "value": " D60ECDFAB87C4B3FA857DFFB99D83B1CEC7D424979CC9C5187152B6685735BF5"}, {"disabled": false, "key": "dev-mirror", "value": "1"}], "method": "GET", "baseUrl": "https://test-api-server.allsaints.top", "body": {}, "auth": {"type": "<PERSON><PERSON><PERSON>", "noauth": []}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "commonScript.assertion", "type": "text/javascript", "exec": ["", "  ____string2Array = function(value) {", "    if(typeof value === 'object'){", "      return value;", "    }", "    try {", "      return JSON.parse(value);", "    } catch (e) {", "      return value;", "    }", "  }", "  ____string2Number = function(value, errorMsg) {", "   if(typeof value !== 'string'){", "     return value;", "   }", "   if (/^\\-?\\d+$/.test(value)) {", "       return parseInt(value);", "   } else if (/^\\-?\\d+\\.\\d+$/.test(value)) {", "       return parseFloat(value);", "   } else {", "       throw new Error(errorMsg || '数据类型不匹配')", "   }", " }", "", "  ____formatValues = function(value, stringCompareValue, comparison) {", "   try{", "     const isNumberComparisons = ['isBelow', 'isAtMost', 'isAbove', 'isAtLeast'].includes(comparison);", "     const isCollectionComparisons = ['isOneOf', 'notOneOf'].includes(comparison);", "     let compareValue;", "     switch (typeof value) {", "         case 'string':", "             if (isNumberComparisons) {", "                value = ____string2Number(value);", "                compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "                compareValue = ____string2Array(stringCompareValue);", "             } else if (comparison === 'exists' || comparison === 'notExist') {", "                value = /^({%.*%})|({{.*}})$/.test(value) ? undefined : value;", "             } else {", "                compareValue = stringCompareValue;", "             }", "             break;", "         case 'object':", "             const isArray = value instanceof Array;", "             if (isNumberComparisons) {", "                 throw new Error('object 类型的值不能使用 ' + comparison + ' 方式比较')", "             } else if (isCollectionComparisons && isArray) {", "              throw new Error('array 类型的值不能使用 ' + comparison + ' 方式比较')", "            } else if (", "              isArray &&", "              comparison === 'include' &&", "              value.includes(stringCompareValue)", "            ) {", "              compareValue = stringCompareValue;", "            } else {", "              try {", "                  compareValue = JSON.parse(stringCompareValue);", "              } catch (e) {", "                  compareValue = stringCompareValue;", "              }", "            }", "             break;", "         case 'boolean':", "             if (isNumberComparisons || isCollectionComparisons) {", "                 throw new Error('boolean 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'true' ? true : (stringCompareValue === 'false' ? false : stringCompareValue);", "             break;", "           case 'bigint':", "           case 'number':", "             if (isNumberComparisons) {", "               compareValue = ____string2Number(stringCompareValue);", "             } else if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "              value = '' + value;", "            } else {", "               compareValue = stringCompareValue;", "               value = '' + value;", "             }", "             break;", "         case 'null':", "             if (isNumberComparisons) {", "                 throw new Error('null 类型的值不能使用 ' + comparison + ' 方式比较')", "             }", "             compareValue = stringCompareValue === 'null' ? null : stringCompareValue;", "             break;", "         default:", "            if (isCollectionComparisons) {", "              compareValue = ____string2Array(stringCompareValue);", "            } else {", "              compareValue = stringCompareValue;", "            }", "            break;", "     }", "     return { compareValue, value };", "   } catch(e) {", "     console.log(e);", "     throw e;", "   }", " }", " "]}}, {"listen": "test", "script": {"id": "postProcessors.0.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`测试场景-获取大数据每日推荐内容，查看是否与app推荐内容匹配`);", "      pm.test(formattedName, async function(done) {", "        try {", "          const value = pm.response.text();", "          ", "    const compareValue = await ____replaceIn(\"\");", "    const formattedValues = ____formatValues(value, compareValue, 'exists');", "  ", "          pm.expectWithKey({key:'undefined',value: value === null ? '' : formattedValues.value }).to.exist", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.assertion", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        ", "  async function ____replaceIn(value) {", "    if (typeof pm.variables.replaceInAsync === 'function') {", "      return await pm.variables.replaceInAsync(value);", "    }", "    return pm.variables.replaceIn(value);", "  };", "", "  ;(async () => {", "    try {", "      const formattedName = await ____replaceIn(`$.code 等于 0000`);", "      pm.test(formattedName, async function(done) {", "        try {", "          ", "          const expression = await ____replaceIn(`$.code`);", "          const JSONPath = require('jsonpath-plus').JSONPath;", "          let jsonData;", "          try {", "            jsonData = pm.response.json();", "          } catch(e) {", "            let text = pm.response.text();", "            const xml2js = require('xml2js');", "            function removeXmlUselessPrefixAndSuffix(text) {", "              const start = text.indexOf('<');", "              const end = text.lastIndexOf('>');", "              if (start === -1 || end === -1) {", "                return text;", "              }", "              return text.substring(start, end + 1);", "            }", "            text = removeXmlUselessPrefixAndSuffix(text);", "            let json;", "            let xmlParseOptions = {", "                explicitArray: false,", "                trim: true,", "                ignoreAttrs: true,", "            };", "            xml2js.parseString(text, xmlParseOptions, function (_, result) {", "                jsonData = result;", "            });", "          }", "          let value = JSONPath({", "            json: j<PERSON><PERSON><PERSON>,", "            path: expression,", "            wrap: false", "          });", "", "          if (false && undefined !== undefined) {", "            if (Array.isArray(value)) {", "              value = Number(undefined) >= 0 ? value[undefined] : value[value.length + Number(undefined)];", "            } else {", "              value = undefined;", "            }", "          }", "", "          ", "          ", "    const compareValue = await ____replaceIn(`0000`);", "    const formattedValues = ____formatValues(value, compareValue, 'equal');", "  ", "          pm.expect(formattedValues.value).to.eql(formattedValues.compareValue);", "          done();", "        } catch (err) {", "          done(err);", "        }", "      });", "    } catch(e) {", "      setImmediate(() => { throw e });", "    }", "  })();", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.2.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        var jsonData = pm.response.json();", "var ids = [];", "var len = jsonData.data.length;", "", "for(i = 0; i < len; i++)", "{", "    ids.push(parseInt(jsonData.data[i].id));", "}", "var idLen = ids.length;", "var recomId = pm.variables.get(\"recommendID\");", "", "console.log(\"id:\"+ids.sort());", "console.log(\"recommendID:\"+recomId.sort());", "", "var flag;", "pm.test(\"每日推荐内容和大数据内容匹配\", () => {", "  flag = 0;", "  for(i = 0;i < idLen; i++)", "  {", "    if(recomId.include(ids[i]))", "    {", "      flag = 1;", "      break;", "    }", "  }", "  pm.expect(flag).to.deep.equal(1)", "});", "      "]}}], "responseDefinition": {"id": 588963163, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "cpId": {"type": "integer"}, "coverSmall": {"type": "string"}, "coverMiddle": {"type": "string"}, "coverLarge": {"type": "string"}, "actors": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}}, "files": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "fileSize": {"type": "integer"}, "bitrateType": {"type": "integer"}, "format": {"type": "string"}, "duration": {"type": "integer"}, "dv": {"type": "integer"}, "ov": {"type": "integer"}, "index": {"type": "integer"}}, "required": ["id", "fileSize", "bitrateType", "format", "duration", "dv", "ov", "index"], "x-apifox-orders": ["id", "fileSize", "bitrateType", "format", "duration", "dv", "ov", "index"]}}, "album": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "required": ["id", "name"], "x-apifox-orders": ["id", "name"]}, "lyricUrl": {"type": "string"}, "favorite": {"type": "integer"}, "setAsh": {"type": "integer"}, "mv": {"type": "object", "properties": {"id": {"type": "integer"}}, "required": ["id"], "x-apifox-orders": ["id"]}, "vip": {"type": "integer"}, "priceType": {"type": "integer"}, "pureAudio": {"type": "integer"}, "commentSwitchStatus": {"type": "integer"}, "ringtoneDpLink": {"type": "string"}, "creator": {"type": "object", "properties": {}, "x-apifox-orders": []}, "releaseDate": {"type": "integer"}, "ringtoneDpLinkType": {"type": "integer"}, "songTag": {"type": "object", "properties": {"tagID": {"type": "integer"}, "tagType": {"type": "integer"}, "tagName": {"type": "string"}, "link": {"type": "string"}, "algo": {"type": "object", "properties": {"type": {"type": "string"}, "score": {"anyOf": [{"type": "integer"}, {"type": "number"}]}}, "required": ["type", "score"], "x-apifox-orders": ["type", "score"]}}, "required": ["tagID", "tagType", "tagName", "link", "algo"], "x-apifox-orders": ["tagID", "tagType", "tagName", "link", "algo"]}, "dolby": {"type": "integer"}, "stereoIconStatus": {"type": "integer"}}, "required": ["id", "name", "cpId", "coverSmall", "coverMiddle", "coverLarge", "actors", "files", "album", "lyricUrl", "favorite", "<PERSON><PERSON><PERSON>", "mv", "vip", "priceType", "pureAudio", "commentSwitchStatus", "ringtoneDpLink", "creator", "releaseDate", "ringtoneDpLinkType", "songTag"], "x-apifox-orders": ["id", "name", "cpId", "coverSmall", "coverMiddle", "coverLarge", "actors", "files", "album", "lyricUrl", "favorite", "<PERSON><PERSON><PERSON>", "mv", "vip", "priceType", "pureAudio", "commentSwitchStatus", "ringtoneDpLink", "creator", "releaseDate", "ringtoneDpLinkType", "songTag", "dolby", "stereoIconStatus"]}}}, "required": ["code", "message", "data"], "x-apifox-orders": ["code", "message", "data"]}, "defaultEnable": true, "ordering": 1, "description": "", "mediaType": "", "headers": [], "oasExtensions": "", "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {}}, "overrideRequesterOptions": {"strictSSL": false}, "metaInfo": {"blockId": "821e140c-4fd9-40fd-b9a5-26ac6e5ed1b2", "id": "57ac3e50-a915-5099-b84e-369b569c2a4f", "type": "http", "name": "查看app每日推荐内容与大数据内容是否匹配", "projectId": 4800107, "relatedId": 6669820, "environmentId": 24915297, "blockNumber": 4, "httpApiId": 246587621, "httpApiCaseId": 275589725, "httpApiName": "首页-每日推荐", "httpApiPath": "/api/v2/recommend/dailySongTags", "httpApiMethod": "get", "httpApiCaseName": "首页-每日推荐"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false, "disabledSystemHeaders": {}, "disableCookies": false}}, {"id": "945fadf4-582d-529a-928d-66e966512af4", "type": "if", "metaInfo": {"id": "945fadf4-582d-529a-928d-66e966512af4", "type": "if", "scopeType": "end", "scopeStartId": "36508579-8954-5b4a-aaef-00635ddbb185"}}, {"id": "f8bf6a6b-70c1-5440-9c9c-086193b15e07", "type": "group", "metaInfo": {"id": "f8bf6a6b-70c1-5440-9c9c-086193b15e07", "type": "group", "scopeType": "end", "scopeStartId": "53a25cce-3ee6-5e40-8221-3a6d9aca66fc"}}], "name": "reco"}], "info": {"name": "reco"}, "dataSchemas": {}, "mockRules": {"rules": [], "enableSystemRule": true}, "environment": {"id": 24915297, "name": "测试环境", "baseUrl": "https://test-upmp.allsaints.group", "baseUrls": {"default": "https://test-upmp.allsaints.group", "308c62cb-9cc2-418f-bb3b-974a33462e34": "https://test-upmp.allsaints.group", "a47196e6-7551-487d-b543-232e55e09683": "https://test-api-server.allsaints.top", "21211e65-3c46-4c63-b963-7a7a65929263": "https://test-api-oppo-bgm.allsaints.top", "d840ff7e-32d9-4497-8809-f2f124dde270": "https://test-logger-web.allsaints.group", "7bd03f4f-8d2d-47f1-b108-ba5e5bffdbfa": "http://10.194.20.102:8080", "5c1c2eb9-bcf3-4222-95ba-8f635ec27bb3": "http://10.171.3.96:8080", "8a734824-b5a5-4c36-bdf0-233a7d3dfe54": "http://10.171.2.133:8080", "c13fae2a-5dd2-4045-b574-725d20ccf2d6": "http://gw-cms.allsaints-internal.com", "84bc06a8-d32b-48b7-b0e2-9ed78b05147c": "", "a6d5521c-eb4f-416c-ba0d-2cec8a5b9525": "https://test-local-push.allsaints.top", "48734eb2-f8a6-4d94-ba7c-8f480ed34ac7": "https://test-h5-oppo-mbi.allsaints.top", "5266cc13-50f6-459a-b0b4-e9bd4b016e4d": "https://test-welfare-api.allsaintsmusic.com", "88eb862b-ac30-4d66-bf76-a640e6a8f1d0": "https://test-pay-oppo.allsaints.top", "2e740854-ab40-46d2-a3fa-a9b2fcc88b0a": "https://test-music-platform-ui.allsaints.group", "ec7be0b3-1eb9-4f73-aaae-f4aa5212b816": "gw-reco.allsaints-internal.com", "eb35bc81-9704-4e80-9e55-c5f1a2fc66b2": "https://test-h5-oppo-mbi.allsaints.top", "7c318b03-a1c8-4f5c-bfe7-9f4266835e50": "https://test-rms.allsaints.group", "7441c433-7298-4d9c-9e9a-1850c7d97541": "https://test-ringtone.allsaints.top/", "91dcd344-c529-42bc-aabd-7859db7a4d99": "https://test-mms.allsaints.top", "7d8f72ab-e100-445b-aee5-bc950abe3539": "http://211.139.189.130:5818"}, "variable": {"id": "4d520b41-9f8d-4e0f-9867-3c882263541d", "name": "测试环境", "values": [{"type": "any", "value": "test", "key": "env", "isBindInitial": true, "initialValue": "test"}, {"type": "any", "value": "292", "key": "uid", "isBindInitial": true, "initialValue": "292"}, {"type": "any", "value": "CN", "key": "region", "isBindInitial": true, "initialValue": "CN"}, {"type": "any", "value": "1001", "key": "channel", "isBindInitial": true, "initialValue": "1001"}, {"type": "any", "value": "cmsAutoTest", "key": "username", "isBindInitial": true, "initialValue": "cmsAutoTest"}, {"type": "any", "value": "qQxYlm+pacFXjDi8Kxul7+0Ub3cNDnYh6Hx5xDg=", "key": "password", "isBindInitial": true, "initialValue": "qQxYlm+pacFXjDi8Kxul7+0Ub3cNDnYh6Hx5xDg="}, {"type": "any", "value": "C:\\Users\\<USER>\\AppData\\Roaming\\apifox\\ExternalPrograms\\examples/oppo/data/songList_picture.jpg", "key": "fileName", "isBindInitial": false, "initialValue": ""}, {"type": "any", "value": "uY-d0sqVOEpUdMesuI2UswAV7TczI9gt0sZuXHxosUWnzK18MBdcZeqIx98l8aRaQvA1Oo3VW9VWZTNq0Vm6I-UU2GxzzjEwFRVkfMi9k3LRbTaBX36DofzRSdv2EckY", "key": "access_token", "isBindInitial": false, "initialValue": ""}, {"type": "any", "value": "4PmiZe_2I-RtKGXXXdoeNMmhZ2pA4TYeZYFnxb_09Nkzd3VSQSNIylFanXw_TSleqFvK9ZE_McFGA2jXypFvz6aoMnrQY4iJFrbaAfKtPRoSAq2uvbHzoJdogN4e-p9c", "key": "refresh_token", "isBindInitial": false, "initialValue": ""}, {"type": "any", "value": "0", "key": "vip_time", "isBindInitial": false, "initialValue": ""}, {"type": "any", "value": "", "key": "bgm_headers", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "bgm_appid", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "ultimate_headers", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "qingting_headers", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "ximalaya_body", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "HomePageId", "isBindInitial": true, "initialValue": "43988"}, {"type": "any", "value": "", "key": "YueKuId", "isBindInitial": true, "initialValue": "1306"}, {"type": "any", "value": "", "key": "TingshuHomePageId", "isBindInitial": true, "initialValue": "24254"}, {"type": "any", "value": "", "key": "HomePageJgqId", "isBindInitial": true, "initialValue": "43989"}, {"type": "any", "value": "", "key": "YinYueZhuanQuId", "isBindInitial": true, "initialValue": "15022"}, {"type": "any", "value": "", "key": "Host", "isBindInitial": true, "initialValue": "test-api-server.allsaints.top"}, {"type": "any", "value": "", "key": "variable_key", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "Apple", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "labelname", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "allLabels", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "tabLabel", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "jgq_id", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "*************", "key": "mongoDB_ip", "isBindInitial": false, "initialValue": ""}, {"type": "any", "value": "rwuser", "key": "mongoDB_username", "isBindInitial": false, "initialValue": "rwuser"}, {"type": "any", "value": "^BtCjnjQMq2T", "key": "mongoDB_password", "isBindInitial": false, "initialValue": "^BtCjnjQMq2T"}, {"type": "any", "value": "pms_meta", "key": "mongoDB_pms", "isBindInitial": false, "initialValue": "pms_meta"}, {"type": "any", "value": "admin", "key": "mongoDB_admin", "isBindInitial": false, "initialValue": "admin"}]}, "requestProxyAgentSettings": {"default": {"agentId": 2}, "308c62cb-9cc2-418f-bb3b-974a33462e34": {"agentId": 2}, "a47196e6-7551-487d-b543-232e55e09683": {"agentId": 2}, "21211e65-3c46-4c63-b963-7a7a65929263": {"agentId": 2}, "d840ff7e-32d9-4497-8809-f2f124dde270": {"agentId": 2}, "7bd03f4f-8d2d-47f1-b108-ba5e5bffdbfa": {"agentId": 2}, "5c1c2eb9-bcf3-4222-95ba-8f635ec27bb3": {"agentId": 2}, "8a734824-b5a5-4c36-bdf0-233a7d3dfe54": {"agentId": 2}, "c13fae2a-5dd2-4045-b574-725d20ccf2d6": {"agentId": 2}, "84bc06a8-d32b-48b7-b0e2-9ed78b05147c": {"agentId": 2}, "a6d5521c-eb4f-416c-ba0d-2cec8a5b9525": {"agentId": 2}, "5266cc13-50f6-459a-b0b4-e9bd4b016e4d": {"agentId": 2}, "48734eb2-f8a6-4d94-ba7c-8f480ed34ac7": {"agentId": 2}, "88eb862b-ac30-4d66-bf76-a640e6a8f1d0": {"agentId": 2}, "2e740854-ab40-46d2-a3fa-a9b2fcc88b0a": {"agentId": 2}, "ec7be0b3-1eb9-4f73-aaae-f4aa5212b816": {"agentId": 2}, "eb35bc81-9704-4e80-9e55-c5f1a2fc66b2": {"agentId": 2}, "7c318b03-a1c8-4f5c-bfe7-9f4266835e50": {"agentId": 2}, "7441c433-7298-4d9c-9e9a-1850c7d97541": {"agentId": 2}, "91dcd344-c529-42bc-aabd-7859db7a4d99": {"agentId": 2}, "343c36aa-**************-6b81d2fbd9b9": {"agentId": 2}, "7d8f72ab-e100-445b-aee5-bc950abe3539": {"agentId": 2}}, "type": "normal", "parameter": {"header": [], "query": [], "body": [], "cookie": []}}, "globals": {"baseUrl": "", "baseUrls": {}, "variable": {"id": "a9bd4902-0b69-476f-b380-e1473fa01de1", "values": [{"type": "any", "value": "", "key": "access_token", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "uid", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "region", "isBindInitial": true, "initialValue": ""}, {"type": "any", "value": "", "key": "channel", "isBindInitial": true, "initialValue": ""}]}, "parameter": {"header": [], "query": [], "body": [], "cookie": []}}, "isServerBuild": false, "isTestFlowControl": true, "projectOptions": {"enableJsonc": false, "enableBigint": false, "responseValidate": true, "isDefaultUrlEncoding": 2, "enableTestScenarioSetting": false, "enableYAPICompatScript": false, "publishedDocUrlRules": {"defaultRule": "RESOURCE_KEY_ONLY", "resourceKeyStandard": "LEGACY"}, "folderShareExpandModeSettings": {"expandId": [], "mode": "AUTO"}, "mockSettings": {"engine": "fakerjs"}, "language": "zh-CN"}}
# -*- coding:utf-8 -*-
# @Time: 2024/7/31 16:28
# @Author: wenjie.hu
# @Email: <EMAIL>
# 接收喜马拉雅长音频更新通知 nacos配置文件：podcaster-importer-test.yml

import hashlib
from urllib.parse import urlencode
from collections import OrderedDict
import sys
import json
import time


def generate_ximalaya_sig(params, app_secret):
    """
    生成喜马拉雅推送签名

    Args:
        params (dict): 请求参数字典
        app_secret (str): 应用秘钥

    Returns:
        str: 签名字符串
    """
    # 移除 sig 参数
    sig = params.pop('sig', None)

    # 对参数进行字典序排序
    sorted_params = OrderedDict(sorted(params.items()))

    # 拼接参数字符串
    param_str = urlencode(sorted_params, doseq=True)

    # 添加应用秘钥
    sign_str = f"{param_str}&app_secret={app_secret}"

    # 计算 MD5 哈希值
    md5_hash = hashlib.md5(sign_str.encode('utf-8'))
    sig = md5_hash.hexdigest().lower()

    return sig


if __name__ == '__main__':
    # 使用示例

    params_js = sys.argv[1]  # 获取脚本传入的body内容
    params = json.loads(params_js)  # 把传入的json字符串转换为字典格式

    # 示例
    # params = {
    #     'push_type': '2',
    #     'app_key': 'b8e31fc8e039f26c9f9a56fb1124b6bb',
    #     'updated_at': '1686133800000',
    #     'subordinated_album_id': '74142124',
    #     'id': '639255019',
    #     'is_paid': 'true',
    #     'is_online': 'true',
    #     'offline_reason_type': '0',
    #     'nonce': '2cb4ce7ad56e4e8caa99e9807742258f',
    #     'timestamp': '1686133811336',
    #     'sig': '47c92c50ff4d198647aaaabd8cd6328d'  # 这个值只是用于验证，实际请求时不需要提供
    # }

    app_secret = '8b00401b2867c0558a4737aa326705c0'
    app_key = "b8e31fc8e039f26c9f9a56fb1124b6bb"
    params['app_key'] = app_key
    current_time = time.time()
    milliseconds = int(current_time * 1000)
    params['timestamp'] = milliseconds
    # print(params) # 调试使用
    sig = generate_ximalaya_sig(params, app_secret)
    res = {
        'sig': sig,
        'app_key': app_key,
        'timestamp': milliseconds
    }
    print(res)

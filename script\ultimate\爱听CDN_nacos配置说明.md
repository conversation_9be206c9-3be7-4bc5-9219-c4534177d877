
# 需要在nacos配置里添加对应的publicKey
app:
  ultimate:
    publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC0O+k+Yh9Rv7vnppIJrwsPpWQ6MJTvn6HfiJdiyFWqQLM6hMy/15tbThkYLoFtNzO/Frx2AhwzHyS5trK8XKM70BsdfAWUIwp7ztJz9UkKsuVasqxGwI/rajP7i3LhWoPrl27oqv9Rk3vfTr2ZpFHpKrZazsQc5FkhiyhH0VlaUQIDAQAB


# java脚本里的key:
PRIVATE_KEY = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALQ76T5iH1G/u+emkgmvCw+lZDowlO+fod+Il2LIVapAszqEzL/Xm1tOGRgugW03M78WvHYCHDMfJLm2srxcozvQGx18BZQjCnvO0nP1SQqy5VqyrEbAj+tqM/uLcuFag+uXbuiq/1GTe99OvZmkUekqtlrOxBzkWSGLKEfRWVpRAgMBAAECgYBa429+xwqWNgMzEVvJyFzimfwOIBurLyv5Rq9Y4D/a6F+5neiGwdqVU6/x8tnP0qukrHmDZRMFcKobgRdnR6wEkAt1XEiR3wuCGOmNXA0elXUQRMnsNKHyoouE2RN3trqFElrkkwBdDNVihmouoG5k679i0WeSQRl/fAmqOMdUcQJBANcvrdlZRYCGC5Nyg7a3jarT5lEy1AY0W/QkaRcYKoCgWWVorNGpRfEs/p1KS+HhoTxR7J95njVDCrxcvE3FQBUCQQDWayJ8jiCUZK+nTbO8MrvHKqOkBPptWFIJ6qyrnMY/sZxGJh/6utw2BbqPkEgPuJSB/8YG3nSL/5z7K9OgYcRNAkAaoO3UjbWKGAxqrKsb+07uqtY/ihiuw9/1MGRI1Va9IBqv7+oi792V4MmJUV5ej3tSaZjsizJGyQsVECzFOvmZAkEAuglObL9sKRSLCx/x2FI0doPaY48mMQU2eQAyPNvTbqQNsReXG5ZRRlYGHlXTEEDYKUrgaElO2cE4VP0bqsXo6QJAJUNKdmFmxUMUrcBCChFh4PU6Pl0moh37fQHuy8L8wDaWRFLFQh9wglhojTrsZQUTH5s9exV+R27KlzPUPgn5zw==";


# java脚本
在java_script目录内，需打包成jar包才能在jenkins使用（get_headers-1.0-SNAPSHOT.jar）
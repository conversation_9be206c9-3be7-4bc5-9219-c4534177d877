# -*- coding:utf-8 -*-
# @Time: 2024/8/14 15:32
# @Author: wenjie.hu
# @Email: <EMAIL>
# 神经元的sign生成脚本

import hashlib
import hmac
import sys
import json

appsecret = "IWENYObv6x"  # nacos上查询asw-welfare的db.yml配置对应key
body_js = sys.argv[1]  # 获取脚本传入的body内容--必须是完整的body参数！！生成签名需要使用完整body做加密生成
body = json.loads(body_js)  # 把传入的json字符串转换为字典格式

# 从apifox环境变量或其他地方获取这些值
# app_version = "101016030"
# appid = "ws_sue98P"
# ouId = "EB0525A10DBA4E0F9FFD53F4733F41CBdf27cf238362a39772e73c3e694e36f5"
# uid = "o_id_znHsEiNoXtvKzafkQLVRhB"
# duId = "31FD167F24A549AABEAAC8B636633874EC7D424979CC9C51614FA5D65C496CEF"

content = f"app_version={body['app_version']}&appid={body['appid']}&duId={body['duId']}&ouId={body['ouId']}&uid={body['uid']}"
# print(content)

# 使用 HMAC-SHA256 算法计算签名
signature = hmac.new(appsecret.encode(), content.encode(), hashlib.sha256).hexdigest().upper()
sign = f"third|{signature}"

print(sign)

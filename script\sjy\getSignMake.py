# -*- coding:utf-8 -*-
# @Time: 2024/8/14 15:32
# @Author: wenjie.hu
# @Email: <EMAIL>
# 神经元的sign生成脚本
# 直接读取csv文件。批量生产sign给wrk脚本调用
import hashlib
import hmac
import csv

appsecret = "IWENYObv6x"
app_version = "101016030"
appid = "ws_sue98P"
ouId = "EB0525A10DBA4E0F9FFD53F4733F41CBdf27cf238362a39772e73c3e694e36f5"
duId = "31FD167F24A549AABEAAC8B636633874EC7D424979CC9C51614FA5D65C496CEF"


def generate_signature(uid):
    content = f"app_version={app_version}&appid={appid}&duId={duId}&ouId={ouId}&uid={uid}"
    signature = hmac.new(appsecret.encode(), content.encode(), hashlib.sha256).hexdigest().upper()
    return signature


# 读取 hw_data.csv 文件
with open('hw_data.csv', 'r') as file:
    reader = csv.reader(file, skipinitialspace=True)
    data = list(reader)
    header = data[0]  # 保存标题行
    data = data[1:]  # 跳过标题行

# 检查是否有数据
if not data:
    print("hw_data.csv 文件中没有有效数据行。")
else:
    # 遍历数据并生成签名
    new_data = []
    for row in data:
        uid, pid, access_token = row
        signature = generate_signature(uid)
        new_row = [uid, pid, access_token, signature]
        new_data.append(new_row)

    # 将新数据写入新文件 new_hw_data.csv
    with open('new_hw_data.csv', 'w', newline='') as file:
        writer = csv.writer(file)
        writer.writerow(['uid', 'pid', 'access_token', 's'])  # 写入标题行
        writer.writerows(new_data)
    print("新文件 new_hw_data.csv 已生成。")

# -*- coding:utf-8 -*-
# @Time: 2024/8/9 16:33
# @Author: wenjie.hu
# @Email: <EMAIL>
# api的sign生成

import hashlib
import hmac
import time
import uuid
import sys
import json


def format_to_dict(text):
    # 将文本按行分割
    lines = text.split('\n')
    # 创建一个空字典
    data = {}

    # 遍历每一行
    for line in lines:
        # 按冒号分割键值对
        parts = line.split(':')
        if len(parts) == 2:
            # 将键值对添加到字典中
            key = parts[0].strip()
            value = parts[1].strip()
            data[key] = value

    return data


def Sign_data(data):
    signkey = "4544b93610d6aeea857db75a0e63edb4"  # 正式和测试环境均可使用
    # signkey = "eb0926d9135287af022c5d100cdcb89a"

    keys_to_delete = ['nonce', 'ts', 'sign']
    for key in keys_to_delete:
        if key in data:
            del data[key]

    base_params = {
        "nonce": str(uuid.uuid4()).replace("-", ""),
        "ts": str(int(time.time() * 1000)),
        # "uid": data["uid"],
        # "utoken": data["utoken"]
    }
    data.update(base_params)

    sorted_params = dict(sorted(data.items()))
    query_string = ''
    for key, value in sorted_params.items():
        if query_string:
            query_string += '&'
        query_string += key + '=' + value
    hmac_msg = hmac.new(signkey.encode(), query_string.encode(), hashlib.sha1).digest()
    sign = hmac_msg.hex()
    # 添加签名到参数字典中
    sorted_params["sign"] = sign

    # 参数转换
    params_str = ""
    for key, value in sorted_params.items():
        params_str += f"{key}:{value}\n"

    data_dict = format_to_dict(params_str)
    return data_dict


params_js = sys.argv[1]  # 获取脚本传入的params内容--必须是完整的params参数！！生成签名需要使用完整params做加密生成
params = json.loads(params_js)  # 把传入的json字符串转换为字典格式
# params = {
#     'version': '10.16.30',
#     'buildVersion': '40.10.16.30test_d0c77e2_240731',
#     'ak': 'test',
#     'nonce': '5dafba76ad6a4a289c3cc540c4557ec3',
#     'ts': '1723187112504',
#     'uid': 'o_id_EG9pnMP3TWB95ST26tMAzV',
#     'utoken': 'ec99c155652a45d8811bfe73a3eb4ca5',
#     'sign': '3404aa3da5a8f9074e1242aec36b966b889f5524',
# }

print(Sign_data(params))  # print输出是为了给apifox工具读取使用

FROM docker.m.daocloud.io/ubuntu:20.04

# 切换为阿里源, 加速后续下载
RUN sed -i -E "s/[a-zA-Z0-9]+.ubuntu.com/mirrors.aliyun.com/g" /etc/apt/sources.list

# 在此可添加自定义指令
# ==============================================================================
# 安装 Python 3.10.14

# 1. 安装编译Python和Zadig所需要的依赖
RUN apt-get update && DEBIAN_FRONTEND=noninteractive apt-get install -y --no-install-recommends \
    build-essential \
    zlib1g-dev \
    libncurses5-dev \
    libgdbm-dev \
    libnss3-dev \
    libssl-dev \
    libreadline-dev \
    libffi-dev \
    libsqlite3-dev \
    wget \
    libbz2-dev \
    curl \
    git \
    tzdata \
    ca-certificates \
    xz-utils \
    openssh-client

# 2. 下载、编译和安装 Python 3.10.14
#    使用 altinstall 来避免覆盖系统默认的 python3
#    --enable-optimizations 会使得编译变慢，但能提升python约10%的运行性能
#    --with-ensurepip=install 确保pip被安装
RUN wget https://mirrors.huaweicloud.com/python/3.10.14/Python-3.10.14.tgz && \
    tar -xzf Python-3.10.14.tgz && \
    cd Python-3.10.14 && \
    ./configure --enable-optimizations --with-ensurepip=install && \
    make -j$(nproc) && \
    make altinstall && \
    cd .. && \
    rm -rf Python-3.10.14.tgz Python-3.10.14

# 3. 创建 python 和 pip 的软链接
RUN ln -sf /usr/local/bin/python3.10 /usr/local/bin/python && \
    ln -sf /usr/local/bin/python3.10 /usr/local/bin/python3 && \
    ln -sf /usr/local/bin/pip3.10 /usr/local/bin/pip && \
    ln -sf /usr/local/bin/pip3.10 /usr/local/bin/pip3

# 安装 pip 依赖
RUN pip install --no-cache-dir -i https://mirrors.aliyun.com/pypi/simple/ \
    beautifulsoup4==4.13.4 \
    certifi==2025.6.15 \
    charset-normalizer==3.4.2 \
    et-xmlfile==2.0.0 \
    idna==3.10 \
    lxml==6.0.0 \
    numpy==2.2.6 \
    openpyxl==3.1.5 \
    pandas==2.3.0 \
    pip==25.1.1 \
    pycryptodome==3.23.0 \
    python-dateutil==2.9.0.post0 \
    pytz==2025.2 \
    redis==3.5.3 \
    redis-py-cluster==2.1.3 \
    requests==2.32.4 \
    setuptools==80.9.0 \
    six==1.17.0 \
    soupsieve==2.7 \        
    typing-extensions==4.14.0 \
    tzdata==2025.2 \
    urllib3==2.5.0

# ==============================================================================
# 安装 Node.js 20.5.1
# ==============================================================================
RUN wget https://mirrors.huaweicloud.com/nodejs/v20.5.1/node-v20.5.1-linux-x64.tar.xz && \
    tar -xJf node-v20.5.1-linux-x64.tar.xz -C /usr/local && \
    rm node-v20.5.1-linux-x64.tar.xz

# 将 node 的 bin 目录添加到 PATH 环境变量中，这样 node, npm, npx 和全局安装的包 (如 apifox) 都能直接使用
ENV PATH="/usr/local/node-v20.5.1-linux-x64/bin:${PATH}"

# 安装 apifox-cli, --no-optional 参数可以跳过安装不必要的、可能导致构建失败的依赖 (如 ibm_db, kerberos等)
RUN npm i -g apifox-cli --no-optional --registry=https://registry.npmmirror.com/ --verbose

# ==============================================================================

# 以下是 Zadig 所需要的依赖 (已在上面一并安装)

# 修改时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

# 安装 docker client
RUN curl -fsSL "http://resources.koderover.com/docker-27.4.1.tgz" -o docker.tgz &&\
    tar -xvzf docker.tgz &&\
    mv docker/* /usr/local/bin/ && \
    rm docker.tgz 

# 4. 清理编译依赖和apt缓存，减小镜像体积
RUN apt-get purge -y --auto-remove build-essential && \
    apt-get autoremove -y && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* 
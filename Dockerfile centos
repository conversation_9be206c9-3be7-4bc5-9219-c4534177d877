FROM docker.m.daocloud.io/centos:7.8.2003

# 切换为阿里源, 加速后续下载
RUN sed -i -e 's|^mirrorlist=|#mirrorlist=|g' -e 's|^#baseurl=http://mirror.centos.org/|baseurl=http://mirrors.aliyun.com/|g' /etc/yum.repos.d/CentOS-Base.repo && \
    yum install -y wget && \
    wget -O /etc/yum.repos.d/epel.repo http://mirrors.aliyun.com/repo/epel-7.repo && \
    yum clean all && \
    yum makecache

# 在此可添加自定义指令
# ==============================================================================
# 安装 Python 3.10.14

# 1. 安装编译Python和Zadig所需要的依赖
RUN yum update -y && \
    yum groupinstall -y "Development Tools" && \
    yum install -y \
    bzip2-devel \
    gdbm-devel \
    libffi-devel \
    ncurses-devel \
    openssl-devel \
    readline-devel \
    sqlite-devel \
    zlib-devel \
    libxml2-devel \
    libxslt-devel \
    curl \
    git \
    xz \
    openssh-clients \
    ca-certificates \
    tzdata \
    perl \
    perl-IPC-Cmd \
    perl-core \
    openssl \
    openssl-libs && \
    yum clean all

# 2. 安装最新版的OpenSSL，确保Python SSL支持
RUN cd /tmp && \
    wget https://www.openssl.org/source/openssl-1.1.1u.tar.gz && \
    tar -xzvf openssl-1.1.1u.tar.gz && \
    cd openssl-1.1.1u && \
    ./config --prefix=/usr/local/openssl --openssldir=/usr/local/openssl shared zlib && \
    make -j$(nproc) && \
    make install && \
    cd .. && \
    rm -rf openssl-1.1.1u.tar.gz openssl-1.1.1u && \
    echo "/usr/local/openssl/lib" > /etc/ld.so.conf.d/openssl.conf && \
    ldconfig

# 3. 下载、编译和安装 Python 3.10.14，使用新安装的OpenSSL
#    使用 altinstall 来避免覆盖系统默认的 python
RUN wget https://mirrors.huaweicloud.com/python/3.10.14/Python-3.10.14.tgz && \
    tar -xzf Python-3.10.14.tgz && \
    cd Python-3.10.14 && \
    LDFLAGS="-L/usr/local/openssl/lib -L/usr/lib64 -L/usr/lib" \
    CPPFLAGS="-I/usr/local/openssl/include" \
    ./configure --with-ensurepip=install \
                --enable-shared \
                --with-openssl=/usr/local/openssl && \
    make -j$(nproc) && \
    make altinstall && \
    cd .. && \
    rm -rf Python-3.10.14.tgz Python-3.10.14 && \
    echo "/usr/local/lib" > /etc/ld.so.conf.d/python3.10.conf && \
    ldconfig

# 4. 创建 python 和 pip 的软链接
RUN ln -sf /usr/local/bin/python3.10 /usr/local/bin/python && \
    ln -sf /usr/local/bin/python3.10 /usr/local/bin/python3 && \
    ln -sf /usr/local/bin/pip3.10 /usr/local/bin/pip && \
    ln -sf /usr/local/bin/pip3.10 /usr/local/bin/pip3

# 设置运行时环境变量
ENV LD_LIBRARY_PATH="/usr/local/openssl/lib:/usr/local/lib:/usr/lib64:/usr/lib:${LD_LIBRARY_PATH}" \
    OPENSSL_CONF="/usr/local/openssl/openssl.cnf"

# 验证 Python SSL 支持
RUN python -c "import ssl; print('SSL module is available'); print('SSL version:', ssl.OPENSSL_VERSION)" || echo "SSL import failed - continuing build"

# 安装 pip 依赖
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir --trusted-host mirrors.aliyun.com -i http://mirrors.aliyun.com/pypi/simple/ \
    beautifulsoup4==4.13.4 \
    certifi==2025.6.15 \
    charset-normalizer==3.4.2 \
    et-xmlfile==2.0.0 \
    idna==3.10 \
    lxml==6.0.0 \
    numpy==2.2.6 \
    openpyxl==3.1.5 \
    pandas==2.3.0 \
    pip==25.1.1 \
    pycryptodome==3.23.0 \
    python-dateutil==2.9.0.post0 \
    pytz==2025.2 \
    redis==3.5.3 \
    redis-py-cluster==2.1.3 \
    requests==2.32.4 \
    setuptools==80.9.0 \
    six==1.17.0 \
    soupsieve==2.7 \        
    typing-extensions==4.14.0 \
    tzdata==2025.2 \
    urllib3==2.5.0


# ==============================================================================
# 安装 Node.js 16.20.2
# ==============================================================================
RUN wget https://mirrors.huaweicloud.com/nodejs/v16.20.2/node-v16.20.2-linux-x64.tar.xz && \
    tar -xJf node-v16.20.2-linux-x64.tar.xz -C /usr/local && \
    rm node-v16.20.2-linux-x64.tar.xz

# 将 node 的 bin 目录添加到 PATH 环境变量中，这样 node, npm, npx 和全局安装的包 (如 apifox) 都能直接使用
ENV PATH="/usr/local/node-v16.20.2-linux-x64/bin:${PATH}"

# 配置 npm 加速镜像
RUN npm config set registry https://registry.npmmirror.com/ && \
    npm config set disturl https://npmmirror.com/mirrors/node

# 安装 apifox-cli，使用更多参数来确保安装稳定
# --ignore-scripts 跳过所有安装脚本，避免可选依赖安装失败
# --production 只安装生产依赖
# --no-optional 跳过可选依赖
# --no-fund 不显示资金请求消息
# --loglevel=error 只显示错误日志
RUN npm i -g apifox-cli --ignore-scripts --production --no-optional --no-fund --loglevel=error --registry=https://registry.npmmirror.com/ && \
    echo "Apifox CLI installation completed"

# ==============================================================================

# 以下是 Zadig 所需要的依赖 (已在上面一并安装)

# 修改时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

# 安装 docker client
RUN curl -fsSL "http://resources.koderover.com/docker-27.4.1.tgz" -o docker.tgz &&\
    tar -xvzf docker.tgz &&\
    mv docker/* /usr/local/bin/ && \
    rm docker.tgz 

# 4. 保留所有必要的运行时库
RUN yum install -y \
    openssl \
    openssl-libs \
    libffi \
    zlib \
    libxml2 \
    libxslt \
    git && \
    yum clean all && \
    rm -rf /var/cache/yum/* 
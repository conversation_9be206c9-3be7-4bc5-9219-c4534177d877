# -*- coding:utf-8 -*-
# @Time: 2024/8/25 12:34
# @Author: wenjie.hu
# @Email: <EMAIL>


import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
import sys
import json

body_js = sys.argv[1]  # 获取脚本传入的body内容--必须是完整的body参数！！生成签名需要使用完整body做加密生成
uid = json.loads(body_js)  # 把传入的json字符串转换为字典格式
# 替换为您的 uid 值
# uid = "o_id_znHsEiNoXtvKzafkQLVRhB"


# 获取当前毫秒级时间戳
import time
timestamp = int(time.time() * 1000)
# 替换为您的密钥和初始化向量-正式环境
# key = b"NqjpTqqcSxBvppnA"  # 密钥
# iv = b"HdxwgNLkJwILyhZA"  # 初始化向量

# 测试环境
key = b"HdxwgNLkJwILyhZI"  # 密钥
iv = b"NqjpTqqcSxBvppnc"  # 初始化向量

# 构造数据字符串
data = f"{uid}|{timestamp}".encode("utf-8")
# 创建 AES 加密器
cipher = AES.new(key, AES.MODE_CBC, iv)
# 加密数据
ciphertext = cipher.encrypt(pad(data, AES.block_size))
# 将加密后的密文转换为十六进制字符串
hex_ciphertext = ciphertext.hex()
# 使用 URL 安全的 Base64 编码
encoded_ciphertext = base64.urlsafe_b64encode(bytes.fromhex(hex_ciphertext))
print(encoded_ciphertext.decode("utf-8"))



